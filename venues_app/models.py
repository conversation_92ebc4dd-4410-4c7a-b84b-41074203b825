# --- Standard Library Imports ---
from datetime import timedelta
from decimal import Decimal

# --- Django Imports ---
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from django.contrib.postgres.search import SearchVectorField
from django.db.models import Q

# --- Local App Imports ---
from accounts_app.models import CustomUser, ServiceProviderProfile
from utils.image_utils import generate_image_path


def get_venue_main_image_path(instance, filename):
    """Generate upload path for venue main/featured images."""
    return generate_image_path(
        entity_type='venues',
        entity_id=instance.id if instance.id else 'temp',
        image_type='venue_featured',
        filename=filename
    )


def get_venue_gallery_image_path(instance, filename):
    """Generate upload path for venue gallery images."""
    return generate_image_path(
        entity_type='venues',
        entity_id=instance.venue.id if instance.venue_id else 'temp',
        image_type='venue_gallery',
        filename=filename
    )



# --- Category Model ---

class Category(models.Model):
    """Classification categories for venues (Spa, Massage, Salon, etc.)."""
    # Status Constants
    ACTIVE = True
    INACTIVE = False

    # Field Definitions
    category_name = models.CharField(
        _('category name'),
        max_length=100,
        help_text=_('Category name (e.g., Spa, Massage, Salon)')
    )
    slug = models.SlugField(
        _('slug'),
        max_length=120,
        unique=True,
        blank=True,
        null=True,
        help_text=_('URL-friendly slug (auto-generated)')
    )
    category_description = models.TextField(
        _('description'),
        blank=True,
        help_text=_('Optional description of the category')
    )
    is_active = models.BooleanField(
        _('active status'),
        default=ACTIVE,
        help_text=_('Whether this category is active and visible')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Category')
        verbose_name_plural = _('Categories')
        ordering = ['category_name']
        indexes = [
            models.Index(fields=['category_name']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return self.category_name

    def save(self, *args, **kwargs):
        """Automatically generate slug on every save."""
        # Always generate slug from category_name
        self.slug = slugify(self.category_name)

        # Ensure slug uniqueness
        if self.slug:
            base_slug = self.slug
            counter = 1
            while Category.objects.filter(slug=self.slug).exclude(pk=self.pk).exists():
                self.slug = f"{base_slug}-{counter}"
                counter += 1
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        """URL for category-specific venue listings."""
        return reverse('venues_app:category_venues', kwargs={'category_slug': self.slug})

    # Backward compatibility properties
    @property
    def name(self):
        return self.category_name

    @property
    def description(self):
        return self.category_description



# --- Venue Model ---

class Venue(models.Model):
    """Represents a service provider's business location."""
    # Approval Status Choices
    DRAFT = 'draft'
    PENDING = 'pending'
    APPROVED = 'approved'
    REJECTED = 'rejected'

    APPROVAL_STATUS_CHOICES = (
        (DRAFT, _('Draft')),
        (PENDING, _('Pending')),
        (APPROVED, _('Approved')),
        (REJECTED, _('Rejected')),
    )

    # Visibility Status Choices
    ACTIVE = 'active'
    INACTIVE = 'inactive'

    VISIBILITY_CHOICES = (
        (ACTIVE, _('Active')),
        (INACTIVE, _('Inactive')),
    )

    # State Choices for Consistency
    STATE_CHOICES = (
        ('AL', 'Alabama'),
        ('AK', 'Alaska'),
        ('AZ', 'Arizona'),
        ('AR', 'Arkansas'),
        ('CA', 'California'),
        ('CO', 'Colorado'),
        ('CT', 'Connecticut'),
        ('DE', 'Delaware'),
        ('FL', 'Florida'),
        ('GA', 'Georgia'),
        ('HI', 'Hawaii'),
        ('ID', 'Idaho'),
        ('IL', 'Illinois'),
        ('IN', 'Indiana'),
        ('IA', 'Iowa'),
        ('KS', 'Kansas'),
        ('KY', 'Kentucky'),
        ('LA', 'Louisiana'),
        ('ME', 'Maine'),
        ('MD', 'Maryland'),
        ('MA', 'Massachusetts'),
        ('MI', 'Michigan'),
        ('MN', 'Minnesota'),
        ('MS', 'Mississippi'),
        ('MO', 'Missouri'),
        ('MT', 'Montana'),
        ('NE', 'Nebraska'),
        ('NV', 'Nevada'),
        ('NH', 'New Hampshire'),
        ('NJ', 'New Jersey'),
        ('NM', 'New Mexico'),
        ('NY', 'New York'),
        ('NC', 'North Carolina'),
        ('ND', 'North Dakota'),
        ('OH', 'Ohio'),
        ('OK', 'Oklahoma'),
        ('OR', 'Oregon'),
        ('PA', 'Pennsylvania'),
        ('RI', 'Rhode Island'),
        ('SC', 'South Carolina'),
        ('SD', 'South Dakota'),
        ('TN', 'Tennessee'),
        ('TX', 'Texas'),
        ('UT', 'Utah'),
        ('VT', 'Vermont'),
        ('VA', 'Virginia'),
        ('WA', 'Washington'),
        ('WV', 'West Virginia'),
        ('WI', 'Wisconsin'),
        ('WY', 'Wyoming'),
    )

    # Core Relationships
    service_provider = models.OneToOneField(
        ServiceProviderProfile,
        on_delete=models.CASCADE,
        related_name='venue',
        verbose_name=_('service provider'),
        help_text=_('Service provider who owns this venue')
    )

    # Basic Information
    venue_name = models.CharField(
        _('venue name'),
        max_length=255,
        blank=False,
        null=False,
        help_text=_('Name of the venue/business')
    )
    slug = models.SlugField(
        _('slug'),
        max_length=255,
        unique=True,
        blank=True,
        null=True,
        help_text=_('URL-friendly slug (auto-generated)')
    )
    short_description = models.TextField(
        _('description'),
        max_length=500,
        blank=False,
        null=False,
        help_text=_('Brief description of the venue (max 500 characters)')
    )

    # Location Details
    state = models.CharField(
        _('state'),
        max_length=2,
        choices=STATE_CHOICES,
        blank=False,
        null=False,
        help_text=_('State where venue is located')
    )
    county = models.CharField(
        _('county'),
        max_length=100,
        blank=False,
        null=False,
        help_text=_('County where the venue is located')
    )
    city = models.CharField(
        _('city'),
        max_length=100,
        blank=False,
        null=False,
        help_text=_('City where the venue is located')
    )
    street_number = models.CharField(
        _('street number'),
        max_length=20,
        blank=False,
        null=False,
        help_text=_('Street number')
    )
    street_name = models.CharField(
        _('street name'),
        max_length=255,
        blank=False,
        null=False,
        help_text=_('Street name')
    )

    # Geographic Coordinates
    latitude = models.DecimalField(
        _('latitude'),
        max_digits=10,
        decimal_places=8,
        null=True,
        blank=True,
        help_text=_('Latitude coordinate for mapping')
    )
    longitude = models.DecimalField(
        _('longitude'),
        max_digits=11,
        decimal_places=8,
        null=True,
        blank=True,
        help_text=_('Longitude coordinate for mapping')
    )

    # Relationships
    us_city = models.ForeignKey(
        'USCity',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='venues',
        verbose_name=_('US city'),
        help_text=_('Standardized location data'),
        db_index=True  # Add index for faster lookups
    )
    categories = models.ManyToManyField(
        Category,
        through='VenueCategory',
        related_name='venues',
        blank=True,
        verbose_name=_('categories'),
        help_text=_('Categories this venue belongs to')
    )

    # Media
    main_image = models.ImageField(
        _('main image'),
        upload_to=get_venue_main_image_path,
        blank=True,
        null=True,
        help_text=_('Main featured image for the venue (JPEG, PNG, WebP - max 5MB). WebP recommended for best quality and compression.')
    )

    # Contact Information
    phone = models.CharField(
        _('phone number'),
        max_length=20,
        blank=True,
        help_text=_('Contact phone number for the venue')
    )
    email = models.EmailField(
        _('email address'),
        blank=True,
        help_text=_('Contact email address for the venue')
    )
    website_url = models.URLField(
        _('website URL'),
        blank=True,
        help_text=_('Official website URL for the venue')
    )

    # Social Media Links
    instagram_url = models.URLField(
        _('Instagram URL'),
        blank=True,
        help_text=_('Instagram profile URL for the venue')
    )
    facebook_url = models.URLField(
        _('Facebook URL'),
        blank=True,
        help_text=_('Facebook page URL for the venue')
    )
    twitter_url = models.URLField(
        _('Twitter URL'),
        blank=True,
        help_text=_('Twitter profile URL for the venue')
    )
    linkedin_url = models.URLField(
        _('LinkedIn URL'),
        blank=True,
        help_text=_('LinkedIn business page URL for the venue')
    )

    # Email Verification
    email_verified = models.BooleanField(
        _('email verified'),
        default=False,
        help_text=_('Whether the venue email address has been verified')
    )
    email_verification_token = models.CharField(
        _('email verification token'),
        max_length=64,
        blank=True,
        help_text=_('Token for email verification')
    )

    # Operational Details
    operating_hours = models.TextField(
        _('operating hours'),
        max_length=500,
        blank=True,
        help_text=_('Operating hours (e.g., 9AM-5PM daily schedule)')
    )
    opening_notes = models.TextField(
        _('opening notes'),
        max_length=300,
        blank=True,
        help_text=_('Custom notes about opening times')
    )

    # Search & Metadata
    tags = models.CharField(
        _('tags'),
        max_length=255,
        blank=True,
        help_text=_('Keywords for search optimization (comma-separated)'),
        db_index=True
    )
    search_vector = SearchVectorField(
        null=True,
        blank=True,
        editable=False,
        help_text=_('Search optimization field')
    )
    approval_status = models.CharField(
        _('approval status'),
        max_length=20,
        choices=APPROVAL_STATUS_CHOICES,
        default=DRAFT,
        help_text=_('Admin approval status')
    )
    visibility = models.CharField(
        _('visibility'),
        max_length=20,
        choices=VISIBILITY_CHOICES,
        default=ACTIVE,
        help_text=_('Visibility to customers')
    )
    admin_notes = models.TextField(
        _('admin notes'),
        blank=True,
        help_text=_('Internal notes about approval/rejection')
    )

    # Status Tracking
    status_log = models.JSONField(
        default=list,
        editable=False,
        help_text=_('Audit log of status changes')
    )

    # Soft Delete
    is_deleted = models.BooleanField(
        _('deleted status'),
        default=False,
        help_text=_('Mark as deleted instead of actual deletion')
    )

    # Timestamps and Tracking
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)
    approved_at = models.DateTimeField(
        _('approved at'),
        null=True,
        blank=True,
        help_text=_('When the venue was approved')
    )
    rejected_at = models.DateTimeField(
        _('rejected at'),
        null=True,
        blank=True,
        help_text=_('When the venue was rejected')
    )
    last_modified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='modified_venues',
        verbose_name=_('last modified by'),
        help_text=_('User who last modified this venue')
    )
    last_modified_at = models.DateTimeField(
        _('last modified at'),
        auto_now=True,
        help_text=_('When the venue was last modified')
    )

    # Granular Visibility Controls  
    show_contact_info = models.BooleanField(
        _('show contact information'),
        default=True,
        help_text=_('Show phone, email, and website to customers')
    )
    show_operating_hours = models.BooleanField(
        _('show operating hours'),
        default=True,
        help_text=_('Show opening hours to customers')
    )
    show_amenities = models.BooleanField(
        _('show amenities'),
        default=True,
        help_text=_('Show venue amenities and features')
    )
    show_faqs = models.BooleanField(
        _('show FAQs'),
        default=True,
        help_text=_('Show frequently asked questions')
    )
    show_team_members = models.BooleanField(
        _('show team members'),
        default=True,
        help_text=_('Show service provider team members')
    )
    show_social_media = models.BooleanField(
        _('show social media'),
        default=True,
        help_text=_('Show social media links')
    )
    
    # Information Freshness Tracking
    description_updated_at = models.DateTimeField(
        _('description updated at'),
        blank=True,
        null=True,
        help_text=_('When venue description was last updated')
    )
    contact_updated_at = models.DateTimeField(
        _('contact updated at'),
        blank=True,
        null=True,
        help_text=_('When contact information was last updated')
    )
    hours_updated_at = models.DateTimeField(
        _('hours updated at'),
        blank=True,
        null=True,
        help_text=_('When operating hours were last updated')
    )
    amenities_updated_at = models.DateTimeField(
        _('amenities updated at'),
        blank=True,
        null=True,
        help_text=_('When amenities were last updated')
    )
    
    # Information Completeness Score
    completeness_score = models.PositiveIntegerField(
        _('completeness score'),
        default=0,
        help_text=_('Calculated completeness score (0-100)')
    )

    class Meta:
        verbose_name = _('Venue')
        verbose_name_plural = _('Venues')
        ordering = ['-created_at']
        # select_related = ['service_provider', 'us_city']
        # prefetch_related = ['categories', 'images', 'services']
        indexes = [
            models.Index(fields=['approval_status', 'visibility']),
            models.Index(fields=['state', 'county', 'city']),
            models.Index(fields=['latitude', 'longitude']),
            models.Index(fields=['created_at']),
            models.Index(fields=['tags']),
            models.Index(fields=['service_provider', 'is_deleted']),
        ]
        constraints = [
            # Ensure only one venue per service provider (excluding deleted venues)
            models.UniqueConstraint(
                fields=['service_provider'],
                condition=models.Q(is_deleted=False),
                name='unique_venue_per_service_provider'
            ),
            # Ensure venue name uniqueness within same city/state (excluding deleted venues)
            models.UniqueConstraint(
                fields=['venue_name', 'city', 'state'],
                condition=models.Q(is_deleted=False),
                name='unique_venue_name_per_location'
            ),
            # Ensure approved venues have approval timestamp
            models.CheckConstraint(
                check=models.Q(approval_status__in=['draft', 'pending', 'rejected']) | 
                      models.Q(approved_at__isnull=False),
                name='approved_venues_have_timestamp'
            ),
            # Ensure rejected venues have rejection timestamp
            models.CheckConstraint(
                check=models.Q(approval_status__in=['draft', 'pending', 'approved']) | 
                      models.Q(rejected_at__isnull=False),
                name='rejected_venues_have_timestamp'
            ),
            # Ensure coordinate validity
            models.CheckConstraint(
                check=models.Q(latitude__isnull=True) | 
                      (models.Q(latitude__gte=-90) & models.Q(latitude__lte=90)),
                name='valid_latitude_range'
            ),
            models.CheckConstraint(
                check=models.Q(longitude__isnull=True) | 
                      (models.Q(longitude__gte=-180) & models.Q(longitude__lte=180)),
                name='valid_longitude_range'
            ),
            # Ensure venue name is not empty
            models.CheckConstraint(
                check=~models.Q(venue_name=''),
                name='venue_name_not_empty'
            ),
            # Ensure description is not empty  
            models.CheckConstraint(
                check=~models.Q(short_description=''),
                name='venue_description_not_empty'
            ),
        ]

    def __str__(self):
        return f"{self.venue_name} - {self.service_provider.business_name}"

    def clean(self):
        """Enhanced validation with comprehensive checks and data sanitization."""
        errors = {}
        
        # Import sanitization utility
        try:
            from utils.sanitization import sanitize_html, sanitize_text
        except ImportError:
            # Fallback sanitization
            import html
            def sanitize_html(text):
                return html.escape(text) if text else text
            def sanitize_text(text):
                return html.escape(text) if text else text
        
        # Sanitize text fields to prevent XSS
        if self.venue_name:
            self.venue_name = sanitize_text(self.venue_name.strip())
            if not self.venue_name:
                errors['venue_name'] = _('Venue name cannot be empty after sanitization.')
        
        if self.short_description:
            self.short_description = sanitize_html(self.short_description.strip())
            if not self.short_description:
                errors['short_description'] = _('Description cannot be empty after sanitization.')
        
        if self.admin_notes:
            self.admin_notes = sanitize_html(self.admin_notes.strip())
            
        if self.opening_notes:
            self.opening_notes = sanitize_html(self.opening_notes.strip())
            
        if self.tags:
            # Sanitize and validate tags
            sanitized_tags = []
            for tag in self.tags.split(','):
                tag = sanitize_text(tag.strip())
                if tag and len(tag) >= 2:  # Minimum tag length
                    sanitized_tags.append(tag)
            
            if len(sanitized_tags) > 10:  # Maximum tag limit
                errors['tags'] = _('Maximum 10 tags allowed.')
            
            self.tags = ', '.join(sanitized_tags[:10])
        
        # Enhanced unique constraint validation for venue names
        if self.venue_name:
            # Check for duplicate venue names (case-insensitive)
            duplicate_name_query = Venue.objects.filter(
                venue_name__iexact=self.venue_name.strip(),
                is_deleted=False
            )
            if self.pk:
                duplicate_name_query = duplicate_name_query.exclude(pk=self.pk)
            
            if duplicate_name_query.exists():
                existing_venue = duplicate_name_query.first()
                errors['venue_name'] = _(
                    'A venue with this name already exists in {city}, {state}. '
                    'Please choose a different name or add a distinguishing suffix.'
                ).format(
                    city=existing_venue.city,
                    state=existing_venue.state
                )
        
        # Enhanced location validation
        if self.state and self.county and self.city:
            from .utils import validate_location_combination
            is_valid, matched_city, error_msg = validate_location_combination(
                self.state, self.county, self.city
            )
            if not is_valid:
                errors['city'] = error_msg
            elif matched_city and not self.us_city:
                # Auto-link to USCity if found
                self.us_city = matched_city
        
        # Validate contact information format
        if self.phone:
            try:
                from .utils import validate_phone_number
                self.phone = validate_phone_number(self.phone)
            except ValidationError as e:
                errors['phone'] = e.message
        
        if self.email:
            try:
                from .utils import validate_business_email
                self.email = validate_business_email(self.email)
            except ValidationError as e:
                errors['email'] = e.message
        
        if self.website_url:
            try:
                from .utils import validate_website_url
                self.website_url = validate_website_url(self.website_url)
            except ValidationError as e:
                errors['website_url'] = e.message
        
        # Validate social media URLs
        if self.instagram_url:
            try:
                from .utils import validate_social_media_url
                self.instagram_url = validate_social_media_url(self.instagram_url, 'instagram')
            except ValidationError as e:
                errors['instagram_url'] = e.message
        
        if self.facebook_url:
            try:
                from .utils import validate_social_media_url
                self.facebook_url = validate_social_media_url(self.facebook_url, 'facebook')
            except ValidationError as e:
                errors['facebook_url'] = e.message
        
        if self.twitter_url:
            try:
                from .utils import validate_social_media_url
                self.twitter_url = validate_social_media_url(self.twitter_url, 'twitter')
            except ValidationError as e:
                errors['twitter_url'] = e.message
        
        if self.linkedin_url:
            try:
                from .utils import validate_social_media_url
                self.linkedin_url = validate_social_media_url(self.linkedin_url, 'linkedin')
            except ValidationError as e:
                errors['linkedin_url'] = e.message
        
        # Validate coordinates if provided
        if self.latitude is not None:
            if not (-90 <= float(self.latitude) <= 90):
                errors['latitude'] = _('Latitude must be between -90 and 90 degrees.')
        
        if self.longitude is not None:
            if not (-180 <= float(self.longitude) <= 180):
                errors['longitude'] = _('Longitude must be between -180 and 180 degrees.')
        
        # Business logic validation
        if self.approval_status == self.APPROVED and not self.approved_at:
            # Auto-set approval timestamp if not set
            from django.utils import timezone
            self.approved_at = timezone.now()
        
        if self.approval_status == self.REJECTED and not self.rejected_at:
            # Auto-set rejection timestamp if not set
            from django.utils import timezone
            self.rejected_at = timezone.now()
        
        # Service provider can only have one venue validation
        if self.service_provider_id:  # Check service_provider_id instead of service_provider
            existing_venue_query = Venue.objects.filter(
                service_provider_id=self.service_provider_id,  # Use service_provider_id
                is_deleted=False
            )
            if self.pk:
                existing_venue_query = existing_venue_query.exclude(pk=self.pk)
            
            if existing_venue_query.exists():
                errors['service_provider'] = _(
                    'This service provider already has a venue. '
                    'Only one venue per service provider is allowed.'
                )
        
        # Validate status transitions
        if self.pk:  # Only for existing venues
            try:
                original = Venue.objects.get(pk=self.pk)
                if (original.approval_status == self.APPROVED and 
                    self.approval_status == self.DRAFT):
                    errors['approval_status'] = _(
                        'Cannot change status from Approved back to Draft. '
                        'Contact administrator if changes are needed.'
                    )
            except Venue.DoesNotExist:
                pass
        
        # Raise validation errors
        if errors:
            raise ValidationError(errors)

    def generate_unique_slug(self):
        """Generate a unique slug for the venue."""
        base_slug = slugify(self.venue_name)[:250]  # Ensure length limit
        unique_slug = base_slug
        counter = 1
        while Venue.objects.filter(slug=unique_slug).exclude(pk=self.pk).exists():
            unique_slug = f"{base_slug}-{counter}"
            counter += 1
        return unique_slug

    def save(self, *args, **kwargs):
        """Enhanced save method with USCity integration and original functionality."""
        # Generate slug if not provided
        if not self.slug:
            self.slug = self.generate_unique_slug()

        # Default approval status handling
        if not self.pk:  # New venue
            if self.approval_status is None:
                self.approval_status = self.PENDING

        # Ensure visibility is set
        if self.visibility is None:
            self.visibility = self.ACTIVE

        # Handle tags processing
        if self.tags:
            # Clean and validate tags
            tags_list = self.get_tags_list()
            if len(tags_list) > 5:
                raise ValidationError(_('Maximum 5 tags allowed'))
            # Re-join cleaned tags
            self.tags = ', '.join(tag.strip() for tag in tags_list if tag.strip())

        # Enhanced auto-link to USCity if not already linked
        if not self.us_city and self.state and self.county and self.city:
            from .utils import find_matching_uscity
            uscity = find_matching_uscity(self.state, self.county, self.city)
            if uscity:
                self.us_city = uscity
                # Set coordinates from USCity if not already set and USCity has coordinates
                if uscity.latitude and uscity.longitude:
                    if not self.latitude or not self.longitude:
                        self.latitude = uscity.latitude
                        self.longitude = uscity.longitude

        super().save(*args, **kwargs)

    def get_absolute_url(self):
        """URL for venue detail page."""
        return reverse('venues_app:venue_detail', kwargs={'venue_slug': self.slug})

    # Status Properties
    @property
    def is_approved(self):
        return self.approval_status == self.APPROVED

    @property
    def is_draft(self):
        return self.approval_status == self.DRAFT

    @property
    def is_visible(self):
        return (self.visibility == self.ACTIVE and
                self.is_approved and
                self.approval_status != self.DRAFT)

    @property
    def full_address(self):
        """Return the complete address string."""
        address_parts = []
        if self.street_number and self.street_name:
            address_parts.append(f"{self.street_number} {self.street_name}")
        if self.city:
            address_parts.append(self.city)
        if self.county and self.county != self.city:
            address_parts.append(self.county)
        if self.state:
            address_parts.append(self.state)
        return ', '.join(address_parts)

    @property
    def location_display(self):
        """Return location for display purposes."""
        if self.us_city:
            return f"{self.us_city.city}, {self.us_city.state_id}"
        return f"{self.city}, {self.state}" if self.city and self.state else ""

    # Backward compatibility property
    @property
    def name(self):
        """Backward compatibility property for templates."""
        return self.venue_name

    def delete(self, *args, **kwargs):
        """Implement soft delete."""
        self.is_deleted = True
        self.visibility = self.INACTIVE
        self.save()

    def get_tags_list(self):
        """Get cleaned and normalized tags list."""
        return list(set(tag.strip().lower() for tag in self.tags.split(',') if tag.strip())) if self.tags else []

    def get_primary_image(self):
        """Return URL of primary gallery image or first active gallery image."""
        # Check for primary gallery image first
        primary_image = self.images.filter(is_primary=True, is_active=True).first()
        if primary_image:
            return primary_image.image.url

        # Fallback to first active gallery image if no primary set
        first_image = self.images.filter(is_active=True).order_by('order').first()
        if first_image:
            # Auto-assign this image as primary to avoid confusion
            first_image.is_primary = True
            first_image.save(update_fields=['is_primary'])
            return first_image.image.url
            
        # Final fallback to main_image (for backward compatibility - to be deprecated)
        if self.main_image:
            return self.main_image.url
            
        return getattr(settings, 'DEFAULT_VENUE_IMAGE_URL', None)

    def get_gallery_images(self):
        """Return all active gallery images ordered by primary status and order."""
        return self.images.filter(is_active=True).order_by('-is_primary', 'order')

    def get_non_primary_images(self):
        """Return all active non-primary gallery images ordered by order."""
        return self.images.filter(is_active=True, is_primary=False).order_by('order')

    def ensure_primary_image(self):
        """Ensure there's always a primary image if gallery images exist."""
        if not self.images.filter(is_primary=True, is_active=True).exists():
            first_image = self.images.filter(is_active=True).order_by('order').first()
            if first_image:
                first_image.is_primary = True
                first_image.save(update_fields=['is_primary'])
                return first_image
        return None

    # === Enhanced Approval Workflow Methods ===
    
    def get_approval_progress(self):
        """Get venue setup completion progress."""
        from .utils import get_approval_progress
        return get_approval_progress(self)
    
    def check_auto_approval_eligibility(self):
        """Check if venue meets auto-approval criteria."""
        from .utils import check_auto_approval_criteria
        return check_auto_approval_criteria(self)
    
    def apply_auto_approval_if_eligible(self):
        """Apply auto-approval if venue meets all criteria."""
        meets_criteria, reasons = self.check_auto_approval_eligibility()
        
        if meets_criteria and self.approval_status == self.PENDING:
            self.approval_status = self.APPROVED
            self.approved_at = timezone.now()
            self.admin_notes = f"Auto-approved based on quality criteria. Completed on {timezone.now().strftime('%Y-%m-%d %H:%M')}"
            
            # Log status change
            self.status_log.append({
                'status': self.APPROVED,
                'timestamp': timezone.now().isoformat(),
                'type': 'auto_approval',
                'notes': 'Automatically approved - met all quality criteria'
            })
            
            self.save(update_fields=['approval_status', 'approved_at', 'admin_notes', 'status_log'])
            return True
        
        return False
    
    def check_reapproval_required(self, changed_fields):
        """Check if changes require re-approval."""
        from .utils import requires_reapproval
        return requires_reapproval(self, changed_fields)
    
    def trigger_reapproval(self, reason="Significant changes made"):
        """Trigger re-approval process for approved venues."""
        if self.approval_status == self.APPROVED:
            self.approval_status = self.PENDING
            self.approved_at = None
            self.admin_notes = f"Re-approval required: {reason}"
            
            # Log status change
            self.status_log.append({
                'status': self.PENDING,
                'timestamp': timezone.now().isoformat(),
                'type': 'reapproval_required',
                'notes': reason
            })
            
            self.save(update_fields=['approval_status', 'approved_at', 'admin_notes', 'status_log'])

    def calculate_completeness_score(self):
        """Calculate the completeness score for the venue profile."""
        score = 0
        total_possible = 12
        
        # Essential information (40 points total)
        if self.venue_name: score += 4
        if self.short_description and len(self.short_description) >= 50: score += 4
        if self.phone or self.email: score += 3
        if self.state and self.county and self.city: score += 3
        
        # Visual content (25 points total)
        if self.main_image: score += 2
        if self.images.filter(is_active=True).count() >= 2: score += 2
        if self.images.filter(is_active=True).count() >= 4: score += 1
        
        # Services and features (20 points total)  
        if self.services.filter(is_active=True).exists(): score += 2
        if self.amenities.filter(is_active=True).exists(): score += 1
        if self.categories.exists(): score += 1
        
        # Detailed information (15 points total)
        if self.operating_hours_set.exists() or self.operating_hours: score += 1
        if self.faqs.filter(is_active=True).exists(): score += 1
        if self.website_url: score += 1
        
        return min(100, int((score / total_possible) * 100))
    
    def update_completeness_score(self):
        """Update and save the completeness score."""
        self.completeness_score = self.calculate_completeness_score()
        self.save(update_fields=['completeness_score'])
    
    def get_information_freshness(self):
        """Get information freshness indicators for different sections."""
        from django.utils import timezone
        now = timezone.now()
        
        def days_since_update(updated_at):
            if not updated_at:
                return None
            return (now - updated_at).days
        
        return {
            'description': {
                'updated_at': self.description_updated_at,
                'days_ago': days_since_update(self.description_updated_at),
                'is_recent': days_since_update(self.description_updated_at) is not None and days_since_update(self.description_updated_at) <= 30,
                'is_stale': days_since_update(self.description_updated_at) is not None and days_since_update(self.description_updated_at) > 90
            },
            'contact': {
                'updated_at': self.contact_updated_at,
                'days_ago': days_since_update(self.contact_updated_at),
                'is_recent': days_since_update(self.contact_updated_at) is not None and days_since_update(self.contact_updated_at) <= 30,
                'is_stale': days_since_update(self.contact_updated_at) is not None and days_since_update(self.contact_updated_at) > 90
            },
            'hours': {
                'updated_at': self.hours_updated_at,
                'days_ago': days_since_update(self.hours_updated_at),
                'is_recent': days_since_update(self.hours_updated_at) is not None and days_since_update(self.hours_updated_at) <= 30,
                'is_stale': days_since_update(self.hours_updated_at) is not None and days_since_update(self.hours_updated_at) > 90
            },
            'amenities': {
                'updated_at': self.amenities_updated_at,
                'days_ago': days_since_update(self.amenities_updated_at),
                'is_recent': days_since_update(self.amenities_updated_at) is not None and days_since_update(self.amenities_updated_at) <= 30,
                'is_stale': days_since_update(self.amenities_updated_at) is not None and days_since_update(self.amenities_updated_at) > 90
            }
        }
    
    def get_missing_information(self):
        """Get list of missing information that could improve the profile."""
        missing = []
        
        if not self.short_description or len(self.short_description) < 50:
            missing.append({
                'section': 'description',
                'title': 'Venue Description',
                'message': 'Add a detailed description (at least 50 characters)',
                'priority': 'high'
            })
        
        if not (self.phone and self.email):
            missing.append({
                'section': 'contact',
                'title': 'Contact Information',
                'message': 'Add both phone and email for better customer reach',
                'priority': 'medium'
            })
        
        if not self.main_image:
            missing.append({
                'section': 'images',
                'title': 'Main Image',
                'message': 'Upload a main venue image',
                'priority': 'high'
            })
        
        if self.images.filter(is_active=True).count() < 3:
            missing.append({
                'section': 'images',
                'title': 'Gallery Images',
                'message': 'Add more images to showcase your venue',
                'priority': 'medium'
            })
        
        if not self.services.filter(is_active=True).exists():
            missing.append({
                'section': 'services',
                'title': 'Services',
                'message': 'Add your services with descriptions and pricing',
                'priority': 'high'
            })
        
        if not self.website_url:
            missing.append({
                'section': 'website',
                'title': 'Website',
                'message': 'Add your website URL for more credibility',
                'priority': 'low'
            })
        
        if not self.amenities.filter(is_active=True).exists():
            missing.append({
                'section': 'amenities',
                'title': 'Amenities',
                'message': 'List venue amenities and features',
                'priority': 'medium'
            })
        
        if not (self.operating_hours_set.exists() or self.operating_hours):
            missing.append({
                'section': 'hours',
                'title': 'Operating Hours',
                'message': 'Set your operating hours',
                'priority': 'medium'
            })
        
        return missing
    
    def has_complete_contact_info(self):
        """Check if venue has complete contact information."""
        return bool(self.phone and self.email)
    
    def has_social_media_presence(self):
        """Check if venue has any social media links."""
        return bool(self.instagram_url or self.facebook_url or self.twitter_url or self.linkedin_url)
    
    def get_visibility_settings(self):
        """Get current visibility settings for different information sections."""
        return {
            'contact_info': self.show_contact_info,
            'operating_hours': self.show_operating_hours,
            'amenities': self.show_amenities,
            'faqs': self.show_faqs,
            'team_members': self.show_team_members,
            'social_media': self.show_social_media
        }

    def get_approval_timeline(self):
        """Get detailed approval timeline with visual representation data."""
        timeline_events = []
        
        # Initial creation
        timeline_events.append({
            'type': 'created',
            'title': 'Venue Created',
            'description': 'Initial venue setup completed',
            'timestamp': self.created_at,
            'icon': 'fas fa-plus-circle',
            'status': 'completed',
            'user': getattr(self.service_provider, 'user', None),
            'details': 'Venue profile was created and basic information added'
        })
        
        # Parse status log for additional events
        for log_entry in self.status_log:
            event_type = log_entry.get('status', 'unknown')
            timestamp_str = log_entry.get('timestamp', '')
            
            try:
                from django.utils.dateparse import parse_datetime
                timestamp = parse_datetime(timestamp_str) if timestamp_str else None
            except:
                timestamp = None
                
            if event_type == 'pending':
                if log_entry.get('type') == 'reapproval_required':
                    timeline_events.append({
                        'type': 'reapproval_requested',
                        'title': 'Re-approval Required',
                        'description': log_entry.get('reason', 'Significant changes detected'),
                        'timestamp': timestamp,
                        'icon': 'fas fa-exclamation-triangle',
                        'status': 'attention',
                        'user': None,
                        'details': log_entry.get('change_details', ''),
                        'changed_fields': log_entry.get('changed_fields', [])
                    })
                else:
                    timeline_events.append({
                        'type': 'submitted',
                        'title': 'Submitted for Approval',
                        'description': 'Venue submitted for admin review',
                        'timestamp': timestamp,
                        'icon': 'fas fa-paper-plane',
                        'status': 'pending',
                        'user': getattr(self.service_provider, 'user', None),
                        'details': 'Venue information submitted for admin approval'
                    })
                    
            elif event_type == 'approved':
                approval_type = log_entry.get('approval_type', 'manual')
                timeline_events.append({
                    'type': 'approved',
                    'title': 'Approved' if approval_type != 'auto_approval' else 'Auto-Approved',
                    'description': log_entry.get('admin_notes', 'Venue approved for public listing'),
                    'timestamp': timestamp,
                    'icon': 'fas fa-check-circle',
                    'status': 'completed',
                    'user': log_entry.get('approved_by'),
                    'details': f"Approval type: {approval_type.replace('_', ' ').title()}"
                })
                
            elif event_type == 'rejected':
                timeline_events.append({
                    'type': 'rejected',
                    'title': 'Rejected',
                    'description': log_entry.get('rejection_reason', 'Venue rejected'),
                    'timestamp': timestamp,
                    'icon': 'fas fa-times-circle',
                    'status': 'error',
                    'user': log_entry.get('rejected_by'),
                    'details': log_entry.get('rejection_reason', '')
                })
                
            elif event_type == 'info_requested':
                timeline_events.append({
                    'type': 'info_requested',
                    'title': 'Information Requested',
                    'description': 'Admin requested additional information',
                    'timestamp': timestamp,
                    'icon': 'fas fa-question-circle',
                    'status': 'attention',
                    'user': log_entry.get('requested_by'),
                    'details': log_entry.get('info_requested', '')
                })
        
        # Add final approval status if exists
        if self.approved_at and not any(e['type'] == 'approved' for e in timeline_events):
            timeline_events.append({
                'type': 'approved',
                'title': 'Approved',
                'description': self.admin_notes or 'Venue approved for public listing',
                'timestamp': self.approved_at,
                'icon': 'fas fa-check-circle',
                'status': 'completed',
                'user': None,
                'details': 'Venue is now visible to customers'
            })
        elif self.rejected_at and not any(e['type'] == 'rejected' for e in timeline_events):
            timeline_events.append({
                'type': 'rejected',
                'title': 'Rejected',
                'description': self.admin_notes or 'Venue rejected',
                'timestamp': self.rejected_at,
                'icon': 'fas fa-times-circle',
                'status': 'error',
                'user': None,
                'details': self.admin_notes or ''
            })
        
        # Sort by timestamp
        timeline_events.sort(key=lambda x: x['timestamp'] or timezone.now(), reverse=True)
        
        return timeline_events
    
    def get_approval_impact_preview(self):
        """Get preview of how approval status changes affect venue visibility."""
        impact_data = {
            'current_status': self.approval_status,
            'current_visibility': self.visibility,
            'visibility_changes': {},
            'feature_access': {},
            'customer_impact': {},
            'seo_impact': {}
        }
        
        # Define what happens with different approval statuses
        status_impacts = {
            'draft': {
                'visibility': 'Hidden from search results and public listings',
                'booking': 'No booking functionality available',
                'seo': 'Not indexed by search engines',
                'provider_access': 'Full edit access, no restrictions'
            },
            'pending': {
                'visibility': 'Hidden from search but existing bookings still work' if self.approval_status == 'approved' else 'Hidden from all public areas',
                'booking': 'Existing bookings maintained' if self.approval_status == 'approved' else 'No new bookings accepted',
                'seo': 'Existing SEO preserved' if self.approval_status == 'approved' else 'Not indexed',
                'provider_access': 'Limited editing during review process'
            },
            'approved': {
                'visibility': 'Fully visible in search results and public listings',
                'booking': 'Full booking functionality available',
                'seo': 'Indexed and optimized for search engines',
                'provider_access': 'Full access with change monitoring'
            },
            'rejected': {
                'visibility': 'Hidden from all public areas',
                'booking': 'No booking functionality',
                'seo': 'Removed from search engine indexes',
                'provider_access': 'Can edit and resubmit for approval'
            }
        }
        
        impact_data['current_impact'] = status_impacts.get(self.approval_status, {})
        
        # Preview what would happen with status changes
        for status, impact in status_impacts.items():
            if status != self.approval_status:
                impact_data['potential_impacts'] = impact_data.get('potential_impacts', {})
                impact_data['potential_impacts'][status] = impact
        
        # Add specific metrics
        impact_data['metrics'] = {
            'estimated_visibility_change': self._calculate_visibility_impact(),
            'booking_impact': self._calculate_booking_impact(),
            'seo_score_change': self._calculate_seo_impact()
        }
        
        return impact_data
    
    def _calculate_visibility_impact(self):
        """Calculate estimated visibility impact of approval status changes."""
        if self.approval_status == 'approved':
            return {
                'current': '100%',
                'pending': '0%' if not self.approved_at else '20%',  # Existing customers can still find
                'rejected': '0%'
            }
        elif self.approval_status == 'pending':
            return {
                'current': '20%' if self.approved_at else '0%',
                'approved': '100%',
                'rejected': '0%'
            }
        else:
            return {
                'current': '0%',
                'approved': '100%',
                'pending': '0%'
            }
    
    def _calculate_booking_impact(self):
        """Calculate booking functionality impact."""
        booking_count = getattr(self, '_booking_count', 0)  # This would be set by view
        
        if self.approval_status == 'approved':
            return {
                'current': 'Full functionality',
                'potential_loss': f'~{booking_count} bookings at risk if status changes'
            }
        else:
            return {
                'current': 'Limited or no booking access',
                'potential_gain': 'Access to full booking system when approved'
            }
    
    def _calculate_seo_impact(self):
        """Calculate SEO impact of status changes."""
        seo_factors = {
            'has_complete_info': bool(self.venue_name and self.short_description),
            'has_images': self.images.filter(is_active=True).exists(),
            'has_reviews': hasattr(self, 'reviews') and self.reviews.exists(),
            'has_services': self.services.filter(is_active=True).exists()
        }
        
        base_score = sum(seo_factors.values()) * 25  # Max 100
        
        if self.approval_status == 'approved':
            return {
                'current_score': base_score,
                'indexed': True,
                'search_ranking': 'Active'
            }
        else:
            return {
                'current_score': 0,
                'indexed': False,
                'search_ranking': 'Not ranked',
                'potential_score': base_score
            }
    
    def get_approval_guidance(self):
        """Get personalized guidance on how to get approved faster."""
        guidance = {
            'priority_actions': [],
            'recommendations': [],
            'estimated_approval_time': 'Standard review process',
            'auto_approval_eligible': False,
            'completion_tips': []
        }
        
        # Check what's missing for faster approval
        missing_info = self.get_missing_information()
        
        # Priority actions (critical for approval)
        critical_missing = [item for item in missing_info if item.get('priority') == 'high']
        for item in critical_missing:
            guidance['priority_actions'].append({
                'action': item['message'],
                'section': item['section'],
                'impact': 'Critical - Required for approval',
                'estimated_time': '5-10 minutes'
            })
        
        # Recommendations (improve approval chances)
        medium_missing = [item for item in missing_info if item.get('priority') == 'medium']
        for item in medium_missing:
            guidance['recommendations'].append({
                'action': item['message'],
                'section': item['section'],
                'impact': 'Improves approval chances and ranking',
                'estimated_time': '10-15 minutes'
            })
        
        # Check auto-approval eligibility
        meets_auto, reasons = self.check_auto_approval_eligibility()
        guidance['auto_approval_eligible'] = meets_auto
        
        if meets_auto:
            guidance['estimated_approval_time'] = 'Immediate (auto-approval)'
        elif len(critical_missing) == 0:
            guidance['estimated_approval_time'] = '1-2 business days'
        else:
            guidance['estimated_approval_time'] = '3-5 business days after completion'
        
        # Add specific tips based on venue type
        venue_categories = self.categories.all()
        if venue_categories.exists():
            category_name = venue_categories.first().name.lower()
            guidance['completion_tips'] = self._get_category_specific_tips(category_name)
        
        # Add quality tips
        guidance['quality_tips'] = [
            'Use high-quality, well-lit photos for faster approval',
            'Write detailed service descriptions to build customer trust',
            'Add operating hours to improve discoverability',
            'Include contact information for customer confidence',
            'Set competitive pricing based on your local market'
        ]
        
        return guidance
    
    def _get_category_specific_tips(self, category_name):
        """Get category-specific completion tips."""
        tips_by_category = {
            'spa': [
                'Highlight specific treatments and wellness services',
                'Mention hygiene and sanitation protocols',
                'Include information about licensed therapists',
                'Add relaxation amenities (music, aromatherapy, etc.)'
            ],
            'salon': [
                'List specific hair and beauty services',
                'Mention product brands you use',
                'Include stylist specializations',
                'Add before/after photos if available'
            ],
            'fitness': [
                'Detail available equipment and class types',
                'Mention trainer certifications',
                'Include safety protocols and cleanliness measures',
                'Add membership options and trial periods'
            ],
            'restaurant': [
                'Upload appetizing food photos',
                'Include dietary options (vegan, gluten-free, etc.)',
                'Mention signature dishes and chef specialties',
                'Add ambiance and seating information'
            ]
        }
        
        return tips_by_category.get(category_name, [
            'Add detailed descriptions of your unique offerings',
            'Include high-quality photos of your space',
            'Mention any certifications or specializations',
            'Highlight what makes your venue special'
        ])



# --- VenueCategory Relationship Model ---

class VenueCategory(models.Model):
    """Through model for Venue-Category relationships."""
    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='venue_categories',
        verbose_name=_('venue')
    )
    category = models.ForeignKey(
        Category,
        on_delete=models.CASCADE,
        related_name='category_venues',
        verbose_name=_('category')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)

    class Meta:
        verbose_name = _('Venue Category')
        verbose_name_plural = _('Venue Categories')
        unique_together = ['venue', 'category']

    def __str__(self):
        return f"{self.venue.venue_name} - {self.category.name}"



# --- VenueImage Model ---
class VenueImage(models.Model):
    """Gallery images for venues (max 5 per venue)."""
    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='images',
        verbose_name=_('venue'),
        help_text=_('Venue this image belongs to')
    )
    image = models.ImageField(
        _('image'),
        upload_to=get_venue_gallery_image_path,
        help_text=_('Venue image file (JPEG, PNG, WebP - max 5MB). WebP recommended for best quality and compression.')
    )
    order = models.PositiveIntegerField(
        _('display order'),
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text=_('Display order of the image (1-5)')
    )
    caption = models.CharField(
        _('caption'),
        max_length=255,
        blank=True,
        help_text=_('Optional caption for the image')
    )
    is_primary = models.BooleanField(
        _('primary image'),
        default=False,
        help_text=_('Whether this is the primary image for the venue')
    )
    is_active = models.BooleanField(
        _('active status'),
        default=True,
        help_text=_('Whether this image is visible')
    )
    
    # Enhanced metadata fields for better information architecture
    file_size = models.PositiveIntegerField(
        _('file size'),
        null=True,
        blank=True,
        help_text=_('Image file size in bytes')
    )
    width = models.PositiveIntegerField(
        _('image width'),
        null=True,
        blank=True,
        help_text=_('Image width in pixels')
    )
    height = models.PositiveIntegerField(
        _('image height'),
        null=True,
        blank=True,
        help_text=_('Image height in pixels')
    )
    format = models.CharField(
        _('image format'),
        max_length=10,
        blank=True,
        help_text=_('Image file format (JPEG, PNG, WebP)')
    )
    original_filename = models.CharField(
        _('original filename'),
        max_length=255,
        blank=True,
        help_text=_('Original filename when uploaded')
    )
    upload_session_id = models.CharField(
        _('upload session ID'),
        max_length=100,
        blank=True,
        help_text=_('Session ID for tracking upload batches')
    )
    
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Venue Image')
        verbose_name_plural = _('Venue Images')
        ordering = ['-is_primary', 'order', 'created_at']
        constraints = [
            models.UniqueConstraint(
                fields=['venue', 'order'],
                name='unique_venue_image_order',
                deferrable=models.Deferrable.DEFERRED
            ),
            models.UniqueConstraint(
                fields=['venue'],
                condition=models.Q(is_primary=True),
                name='unique_primary_image_per_venue'
            )
        ]

    def __str__(self):
        primary_text = " (Primary)" if self.is_primary else ""
        return f"{self.venue.venue_name} - Image {self.order}{primary_text}"

    @property
    def file_size_mb(self):
        """Return file size in MB with proper formatting."""
        if self.file_size:
            return round(self.file_size / (1024 * 1024), 2)
        return 0

    @property
    def file_size_kb(self):
        """Return file size in KB with proper formatting."""
        if self.file_size:
            return round(self.file_size / 1024, 1)
        return 0

    @property
    def dimensions_display(self):
        """Return formatted dimensions display."""
        if self.width and self.height:
            return f"{self.width} × {self.height}px"
        return "Unknown"

    @property
    def aspect_ratio(self):
        """Calculate and return aspect ratio."""
        if self.width and self.height and self.height > 0:
            return round(self.width / self.height, 2)
        return 0

    @property
    def aspect_ratio_display(self):
        """Return user-friendly aspect ratio display."""
        ratio = self.aspect_ratio
        if ratio == 0:
            return "Unknown"
        
        # Common aspect ratios
        if 1.49 <= ratio <= 1.51:
            return "3:2 (Landscape)"
        elif 1.32 <= ratio <= 1.34:
            return "4:3 (Standard)"
        elif 1.77 <= ratio <= 1.79:
            return "16:9 (Widescreen)"
        elif 0.99 <= ratio <= 1.01:
            return "1:1 (Square)"
        elif ratio > 1:
            return f"{ratio:.2f}:1 (Landscape)"
        else:
            return f"1:{1/ratio:.2f} (Portrait)"

    @property
    def image_category(self):
        """Return image category for better organization."""
        if self.is_primary:
            return "Primary Image"
        else:
            return "Gallery Image"

    @property
    def upload_age_display(self):
        """Return human-readable upload age."""
        from django.utils.timesince import timesince
        return timesince(self.created_at)

    def get_quality_score(self):
        """Calculate image quality score based on dimensions and file size."""
        if not self.width or not self.height:
            return 0
        
        score = 100
        
        # Dimension scoring
        if self.width < 600 or self.height < 400:
            score -= 30
        elif self.width < 1000 or self.height < 600:
            score -= 15
        
        # File size scoring (optimal range: 200KB - 1MB)
        if self.file_size:
            size_kb = self.file_size_kb
            if size_kb < 100:
                score -= 20  # Too compressed
            elif size_kb > 2000:
                score -= 10  # Too large
        
        # Format scoring
        if self.format == 'WebP':
            score += 5
        elif self.format == 'JPEG':
            pass  # No penalty
        elif self.format == 'PNG':
            score -= 5  # Less efficient for photos
        
        return max(0, min(100, score))

    def get_optimization_suggestions(self):
        """Return list of optimization suggestions."""
        suggestions = []
        
        if not self.width or not self.height:
            return suggestions
        
        # Dimension suggestions
        if self.width < 600 or self.height < 400:
            suggestions.append({
                'type': 'warning',
                'category': 'dimensions',
                'message': 'Image resolution is low. Consider using higher resolution images for better display quality.',
                'action': 'Use images with at least 1000×600 pixels'
            })
        
        # File size suggestions
        if self.file_size:
            if self.file_size_kb < 100:
                suggestions.append({
                    'type': 'info',
                    'category': 'compression',
                    'message': 'Image is heavily compressed. Quality may be affected.',
                    'action': 'Consider using less compression for better quality'
                })
            elif self.file_size_kb > 2000:
                suggestions.append({
                    'type': 'warning',
                    'category': 'size',
                    'message': 'Large file size may affect page loading speed.',
                    'action': 'Consider optimizing the image for web use'
                })
        
        # Format suggestions
        if self.format == 'PNG':
            suggestions.append({
                'type': 'info',
                'category': 'format',
                'message': 'PNG format is less efficient for photos. WebP or JPEG would be better.',
                'action': 'Convert to WebP or JPEG format'
            })
        elif self.format == 'JPEG':
            suggestions.append({
                'type': 'info',
                'category': 'format',
                'message': 'Consider using WebP format for better compression and quality.',
                'action': 'Convert to WebP format'
            })
        
        return suggestions

    def populate_metadata_from_file(self):
        """Extract and populate metadata from the image file."""
        if not self.image:
            return
        
        try:
            from PIL import Image
            
            # Store original filename
            if not self.original_filename and hasattr(self.image, 'name'):
                self.original_filename = self.image.name.split('/')[-1]
            
            # Get file size
            if hasattr(self.image, 'size'):
                self.file_size = self.image.size
            
            # Get image dimensions and format
            with Image.open(self.image) as img:
                self.width, self.height = img.size
                self.format = img.format
                
        except Exception as e:
            # Log error but don't fail
            import logging
            logger = logging.getLogger('venues_app')
            logger.warning(f"Failed to extract metadata for image {self.id}: {str(e)}")

    def save(self, *args, **kwargs):
        """Handle primary image logic and auto-assign order."""
        # Extract metadata before saving
        if self.image and not self.width:
            self.populate_metadata_from_file()
        
        # If this is being set as primary, unset other primary images
        if self.is_primary:
            VenueImage.objects.filter(venue=self.venue, is_primary=True).exclude(pk=self.pk).update(is_primary=False)

        # Auto-assign order if not set
        if self.order is None:
            max_order = VenueImage.objects.filter(venue=self.venue).exclude(pk=self.pk).aggregate(
                max_order=models.Max('order')
            )['max_order'] or 0
            self.order = max_order + 1

        super().save(*args, **kwargs)
        
        # After saving, ensure there's always a primary image
        if not VenueImage.objects.filter(venue=self.venue, is_primary=True, is_active=True).exists():
            # Make this image primary if it's the only one or first one
            if not self.is_primary:
                self.is_primary = True
                super().save(update_fields=['is_primary'])

    def clean(self):
        """Enforce maximum of 5 images per venue and validate image file."""
        super().clean()
        if self.venue_id:
            existing = VenueImage.objects.filter(venue=self.venue).exclude(pk=self.pk)
            
            # Check image limit based on user type
            max_images = self.get_max_images_for_venue()
            if existing.count() >= max_images:
                if max_images == 5:
                    raise ValidationError(_('Maximum 5 images allowed per venue. Upgrade to Premium for up to 10 images.'))
                else:
                    raise ValidationError(_(f'Maximum {max_images} images allowed per venue'))

        # Validate image file if present
        if self.image:
            try:
                from utils.image_utils import validate_image_comprehensive
                validate_image_comprehensive(self.image, 'venue_gallery', max_size_kb=5120)  # 5MB
            except ImportError:
                # Fallback to old validation if utils not available
                try:
                    from utils.image_utils import validate_image_extension, validate_image_size
                    validate_image_extension(self.image.name, 'venue_gallery')
                    validate_image_size(self.image, max_size_kb=5120)  # 5MB
                except ImportError:
                    pass  # Skip validation if utils not available
                except ValidationError as e:
                    raise ValidationError({'image': e.message})
            except ValidationError as e:
                raise ValidationError({'image': str(e)})

    def delete(self, *args, **kwargs):
        """Handle image file deletion and primary image reassignment."""
        # Store venue reference before deletion
        venue = self.venue
        was_primary = self.is_primary
        image_order = self.order

        # Delete the actual image file
        if self.image:
            try:
                from utils.image_service import ImageService
                ImageService.delete_image(self.image.path)
            except (ImportError, Exception):
                # Fallback to basic file deletion
                if self.image.storage.exists(self.image.name):
                    self.image.storage.delete(self.image.name)

        super().delete(*args, **kwargs)

        # If this was the primary image, set the first remaining image as primary
        if was_primary:
            first_image = VenueImage.objects.filter(venue=venue, is_active=True).order_by('order').first()
            if first_image:
                first_image.is_primary = True
                first_image.save(update_fields=['is_primary'])
        
        # Reorder remaining images to fill the gap
        self.reorder_after_deletion(venue, image_order)
    
    @classmethod
    def reorder_after_deletion(cls, venue, deleted_order):
        """Reorder images after one is deleted to maintain sequential order."""
        images_to_reorder = cls.objects.filter(
            venue=venue, 
            is_active=True, 
            order__gt=deleted_order
        ).order_by('order')
        
        for image in images_to_reorder:
            image.order -= 1
            image.save(update_fields=['order'])

    def move_up(self):
        """Move this image up in order (decrease order number)."""
        if self.order > 1:
            # Find image with the previous order
            prev_image = VenueImage.objects.filter(
                venue=self.venue, 
                is_active=True, 
                order=self.order - 1
            ).first()
            
            if prev_image:
                # Swap orders
                prev_image.order, self.order = self.order, prev_image.order
                prev_image.save(update_fields=['order'])
                self.save(update_fields=['order'])
                return True
        return False

    def move_down(self):
        """Move this image down in order (increase order number)."""
        max_order = VenueImage.objects.filter(venue=self.venue, is_active=True).aggregate(
            max_order=models.Max('order')
        )['max_order'] or 0
        
        if self.order < max_order:
            # Find image with the next order
            next_image = VenueImage.objects.filter(
                venue=self.venue, 
                is_active=True, 
                order=self.order + 1
            ).first()
            
            if next_image:
                # Swap orders
                next_image.order, self.order = self.order, next_image.order
                next_image.save(update_fields=['order'])
                self.save(update_fields=['order'])
                return True
        return False

    def get_max_images_for_venue(self):
        """Get the maximum number of images allowed for this venue."""
        # Fixed: Remove is_premium reference as it doesn't exist
        # All users get 5 images maximum for now
        return 5


# --- VenueFAQ Model ---
class VenueFAQ(models.Model):
    """Frequently asked questions for venues (max 5 per venue)."""
    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='faqs',
        verbose_name=_('venue'),
        help_text=_('Venue this FAQ belongs to')
    )
    question = models.CharField(
        _('question'),
        max_length=255,
        help_text=_('Frequently asked question')
    )
    answer = models.TextField(
        _('answer'),
        max_length=500,
        help_text=_('Answer to the question (max 500 characters)')
    )
    order = models.PositiveIntegerField(
        _('display order'),
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text=_('Display order of the FAQ (1-5)')
    )
    is_active = models.BooleanField(
        _('active status'),
        default=True,
        help_text=_('Whether this FAQ is visible')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Venue FAQ')
        verbose_name_plural = _('Venue FAQs')
        ordering = ['order', 'created_at']
        unique_together = ['venue', 'order']

    def __str__(self):
        return f"{self.venue.venue_name} - FAQ {self.order}: {self.question[:50]}"

    def clean(self):
        """Enforce maximum of 5 FAQs per venue."""
        super().clean()
        if self.venue_id:
            existing = VenueFAQ.objects.filter(venue=self.venue).exclude(pk=self.pk)
            if existing.count() >= 5:
                raise ValidationError(_('Maximum 5 FAQs allowed per venue'))


# --- Service Model ---
class Service(models.Model):
    """Services offered at a venue (max 7 per venue)."""
    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='services',
        verbose_name=_('venue'),
        help_text=_('Venue where this service is offered')
    )
    service_title = models.CharField(
        _('service title'),
        max_length=255,
        help_text=_('Title/name of the service')
    )
    slug = models.SlugField(
        _('slug'),
        max_length=255,
        blank=True,
        null=True,
        help_text=_('URL-friendly slug (auto-generated or custom)')
    )
    custom_slug = models.SlugField(
        _('custom slug'),
        max_length=255,
        blank=True,
        help_text=_('Custom URL slug (optional). Leave blank to auto-generate from title.')
    )
    short_description = models.TextField(
        _('description'),
        max_length=500,
        blank=True,
        help_text=_('Brief description of the service')
    )

    # Service Categorization
    service_category = models.ForeignKey(
        'ServiceCategory',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='services',
        verbose_name=_('service category'),
        help_text=_('Category that best describes this service')
    )

    # Pricing & Duration
    price_min = models.DecimalField(
        _('minimum price'),
        max_digits=8,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text=_('Minimum price for this service')
    )
    price_max = models.DecimalField(
        _('maximum price'),
        max_digits=8,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        null=True,
        blank=True,
        help_text=_('Maximum price for variable pricing')
    )
    duration_minutes = models.PositiveIntegerField(
        _('duration (minutes)'),
        validators=[MinValueValidator(1), MaxValueValidator(1440)],
        default=60,
        help_text=_('Duration of the service in minutes')
    )

    # Availability Settings
    has_custom_availability = models.BooleanField(
        _('custom availability'),
        default=False,
        help_text=_('Whether this service has different availability from venue hours')
    )
    requires_booking = models.BooleanField(
        _('requires booking'),
        default=True,
        help_text=_('Whether this service requires advance booking')
    )
    max_advance_booking_days = models.PositiveIntegerField(
        _('max advance booking (days)'),
        default=60,
        validators=[MinValueValidator(1), MaxValueValidator(365)],
        help_text=_('Maximum days in advance customers can book')
    )
    min_advance_booking_hours = models.PositiveIntegerField(
        _('min advance booking (hours)'),
        default=2,
        validators=[MinValueValidator(1), MaxValueValidator(168)],
        help_text=_('Minimum hours in advance required for booking')
    )

    # Service Images
    service_image = models.ImageField(
        _('service image'),
        upload_to='services/images/',
        blank=True,
        null=True,
        help_text=_('Optional image for this service (JPEG, PNG, WebP - max 2MB)')
    )

    # Service Tags
    tags = models.ManyToManyField(
        'ServiceTag',
        related_name='services',
        blank=True,  
        verbose_name=_('service tags'),
        help_text=_('Tags for better searchability (e.g., relaxing, deep-tissue, pain-relief)')
    )

    # Status
    is_active = models.BooleanField(
        _('active status'),
        default=True,
        help_text=_('Whether this service is bookable')
    )
    is_featured = models.BooleanField(
        _('featured service'),
        default=False,
        help_text=_('Whether this service should be highlighted')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Service')
        verbose_name_plural = _('Services')
        ordering = ['service_title']
        indexes = [
            models.Index(fields=['service_title']),
            models.Index(fields=['is_active']),
            models.Index(fields=['service_category']),
            models.Index(fields=['is_featured']),
            models.Index(fields=['venue', 'slug']),  # Add composite index for venue-scoped slugs
            models.Index(fields=['venue', 'is_active']),  # Index for active services by venue
        ]
        # Make slug unique per venue instead of globally unique
        constraints = [
            models.UniqueConstraint(
                fields=['venue', 'slug'],
                name='unique_service_slug_per_venue',
                condition=models.Q(slug__isnull=False)
            ),
        ]

    def __str__(self):
        return self.service_title

    def clean(self):
        """Enhanced validation for price range, service limits, and business rules."""
        super().clean()

        # Pricing validation
        if self.price_min is not None:
            if self.price_max and self.price_max < self.price_min:
                raise ValidationError(_('Maximum price cannot be less than minimum price'))
            if self.price_min <= Decimal('0.00'):
                raise ValidationError(_('Price must be greater than zero'))

        # Service limit validation
        if self.venue_id:
            existing = Service.objects.filter(venue=self.venue).exclude(pk=self.pk)
            if existing.count() >= 7:
                raise ValidationError(_('Maximum 7 services allowed per venue'))

        # Custom slug validation
        if self.custom_slug:
            if not self._is_seo_friendly_slug(self.custom_slug):
                raise ValidationError(
                    _('Custom slug must be SEO-friendly: use lowercase letters, numbers, and hyphens only. '
                      'Should be 3-50 characters long and not start/end with hyphens.')
                )
            
            # Check for duplicate custom slug within the same venue
            if self.venue_id:
                existing_custom = Service.objects.filter(
                    venue=self.venue,
                    slug=self.custom_slug
                ).exclude(pk=self.pk)
                if existing_custom.exists():
                    raise ValidationError(
                        _('A service with this URL slug already exists in your venue. Please choose a different slug.')
                    )

        # Service overlap validation - check for similar services
        if self.venue_id and self.service_title:
            similar_services = self._find_similar_services()
            if similar_services:
                similar_names = [s.service_title for s in similar_services[:3]]
                raise ValidationError(
                    _('Similar services already exist in your venue: {}. '
                      'Consider using more specific names or combining similar services.').format(
                        ', '.join(similar_names)
                    )
                )

        # Business hours validation - ensure service fits within venue operating hours
        if self.venue_id and self.duration_minutes:
            venue_hours_conflict = self._check_venue_hours_conflict()
            if venue_hours_conflict:
                raise ValidationError(venue_hours_conflict)

        # Required field validation
        if not self.service_title or len(self.service_title.strip()) < 3:
            raise ValidationError(_('Service title is required and must be at least 3 characters long.'))
        
        if not self.short_description or len(self.short_description.strip()) < 10:
            raise ValidationError(_('Service description is required and must be at least 10 characters long.'))

    def _is_seo_friendly_slug(self, slug):
        """Check if slug is SEO-friendly."""
        import re
        if not slug or len(slug) < 3 or len(slug) > 50:
            return False
        if slug.startswith('-') or slug.endswith('-'):
            return False
        if not re.match(r'^[a-z0-9-]+$', slug):
            return False
        if '--' in slug:  # No consecutive hyphens
            return False
        return True

    def _find_similar_services(self):
        """Find similar services in the same venue using text similarity."""
        if not self.service_title or not self.venue_id:
            return []
        
        import difflib
        current_title = self.service_title.lower().strip()
        similar_services = []
        
        existing_services = Service.objects.filter(venue=self.venue).exclude(pk=self.pk)
        
        for service in existing_services:
            similarity = difflib.SequenceMatcher(
                None, current_title, service.service_title.lower()
            ).ratio()
            
            # Check for exact matches or very similar (>80% similarity)
            if similarity > 0.8:
                similar_services.append(service)
            # Also check for common service keywords
            elif self._has_overlapping_keywords(current_title, service.service_title.lower()):
                similar_services.append(service)
        
        return similar_services

    def _has_overlapping_keywords(self, title1, title2):
        """Check if two service titles have significant keyword overlap."""
        # Common service keywords to check
        keywords1 = set(title1.split())
        keywords2 = set(title2.split())
        
        # Remove common stop words
        stop_words = {'and', 'or', 'the', 'a', 'an', 'with', 'for', 'of', 'in', 'on', 'at'}
        keywords1 = keywords1 - stop_words
        keywords2 = keywords2 - stop_words
        
        if not keywords1 or not keywords2:
            return False
        
        # Check if significant overlap (>50% of keywords match)
        intersection = keywords1.intersection(keywords2)
        return len(intersection) / max(len(keywords1), len(keywords2)) > 0.5

    def _check_venue_hours_conflict(self):
        """Check if service duration conflicts with venue operating hours."""
        if not self.venue_id or not self.duration_minutes:
            return None
        
        try:
            venue_hours = self.venue.operating_hours_set.all()
            if not venue_hours.exists():
                return _('Venue operating hours must be set before creating services. '
                        'Please configure your venue\'s business hours first.')
            
            # Check if service duration is reasonable for venue's daily hours
            min_daily_hours = 24 * 60  # Start with max possible
            for hours in venue_hours:
                if not hours.is_closed and hours.opening and hours.closing:
                    if hours.is_overnight:
                        # Handle overnight hours (e.g., open until 2 AM next day)
                        daily_minutes = (24 * 60) - (hours.opening.hour * 60 + hours.opening.minute) + (hours.closing.hour * 60 + hours.closing.minute)
                    else:
                        daily_minutes = (hours.closing.hour * 60 + hours.closing.minute) - (hours.opening.hour * 60 + hours.opening.minute)
                    
                    min_daily_hours = min(min_daily_hours, daily_minutes)
            
            # If service duration is longer than the shortest operating day
            if self.duration_minutes > min_daily_hours:
                hours_display = f"{min_daily_hours // 60}h {min_daily_hours % 60}m"
                service_display = f"{self.duration_minutes // 60}h {self.duration_minutes % 60}m"
                return _(
                    'Service duration ({}) exceeds your venue\'s shortest operating day ({}). '
                    'Please adjust the service duration or extend your operating hours.'
                ).format(service_display, hours_display)
        
        except Exception:
            # If there's any error checking hours, don't block service creation
            pass
        
        return None

    def generate_slug(self):
        """Generate SEO-friendly slug from service title or use custom slug."""
        if self.custom_slug:
            # Use custom slug if provided and valid
            if self._is_seo_friendly_slug(self.custom_slug):
                return self.custom_slug
            else:
                # Fall back to auto-generation if custom slug is invalid
                pass
        
        # Auto-generate from service title
        base_slug = slugify(self.service_title)
        if not base_slug:
            base_slug = 'service'
        
        # Ensure slug is venue-scoped (not globally unique)
        slug = base_slug
        counter = 1
        while Service.objects.filter(venue=self.venue, slug=slug).exclude(pk=self.pk).exists():
            slug = f"{base_slug}-{counter}"
            counter += 1
        
        return slug

    def save(self, *args, **kwargs):
        """Generate slug and perform additional validation before saving."""
        # Generate slug
        self.slug = self.generate_slug()
        
        # Ensure we have venue_id for validation
        if not self.venue_id and hasattr(self, 'venue'):
            self.venue_id = self.venue.id
        
        super().save(*args, **kwargs)

    # Display Properties
    @property
    def price_display(self):
        if self.price_max and self.price_max != self.price_min:
            return f"${self.price_min} - ${self.price_max}"
        return f"${self.price_min}"

    @property
    def duration_display(self):
        hours, minutes = divmod(self.duration_minutes, 60)
        return f"{hours}h {minutes}m" if hours else f"{minutes}m"

    @property
    def category_display(self):
        """Display service category or fallback to venue category."""
        if self.service_category:
            return self.service_category.name
        # Fallback to venue category
        venue_categories = self.venue.categories.first()
        return venue_categories.category_name if venue_categories else "General Services"

    @property
    def image_url(self):
        """Get service image URL with fallback to venue image."""
        if self.service_image:
            return self.service_image.url
        # Fallback to venue main image
        if self.venue.main_image:
            return self.venue.main_image.url
        return None

    def get_absolute_url(self):
        """Return the URL for this service."""
        return reverse('venues_app:service_detail', kwargs={
            'venue_slug': self.venue.slug,
            'service_slug': self.slug
        })

    def get_similar_services_warning(self):
        """Get warning about similar services for display to user."""
        similar = self._find_similar_services()
        if similar:
            return {
                'has_similar': True,
                'count': len(similar),
                'services': [{'title': s.service_title, 'id': s.id} for s in similar[:3]],
                'message': f"Found {len(similar)} similar service{'s' if len(similar) != 1 else ''} in your venue"
            }
        return {'has_similar': False}

    def get_tag_names(self):
        """Get list of tag names for this service."""
        return list(self.tags.filter(is_active=True).values_list('name', flat=True))

    def get_tags_display(self):
        """Get formatted string of tag names for display."""
        tag_names = self.get_tag_names()
        return ', '.join(tag_names) if tag_names else 'No tags'

    def get_tags_by_type(self):
        """Get tags organized by type."""
        tags_by_type = {}
        for tag in self.tags.filter(is_active=True).select_related():
            tag_type = tag.get_tag_type_display()
            if tag_type not in tags_by_type:
                tags_by_type[tag_type] = []
            tags_by_type[tag_type].append(tag)
        return tags_by_type

    # Backward compatibility
    @property
    def price(self):
        return self.price_min


# --- Service Category Model ---
class ServiceCategory(models.Model):
    """Categories specifically for services (independent of venue categories)."""
    # Status Constants
    ACTIVE = True
    INACTIVE = False

    # Field Definitions
    name = models.CharField(
        _('category name'),
        max_length=100,
        unique=True,
        help_text=_('Service category name (e.g., Massage, Facial, Hair)')
    )
    slug = models.SlugField(
        _('slug'),
        max_length=120,
        unique=True,
        blank=True,
        null=True,
        help_text=_('URL-friendly slug (auto-generated)')
    )
    description = models.TextField(
        _('description'),
        blank=True,
        help_text=_('Optional description of the service category')
    )
    icon_class = models.CharField(
        _('icon class'),
        max_length=50,
        blank=True,
        help_text=_('CSS icon class for display (e.g., fas fa-spa)')
    )
    color_code = models.CharField(
        _('color code'),
        max_length=7,
        blank=True,
        help_text=_('Hex color code for category display (e.g., #FF5722)')
    )
    is_active = models.BooleanField(
        _('active status'),
        default=ACTIVE,
        help_text=_('Whether this category is active and visible')
    )
    sort_order = models.PositiveIntegerField(
        _('sort order'),
        default=0,
        help_text=_('Display order for category listings')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Service Category')
        verbose_name_plural = _('Service Categories')
        ordering = ['sort_order', 'name']
        indexes = [
            models.Index(fields=['is_active']),
            models.Index(fields=['sort_order']),
        ]

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        """Automatically generate slug on every save."""
        # Always generate slug from name
        self.slug = slugify(self.name)

        # Ensure slug uniqueness
        if self.slug:
            base_slug = self.slug
            counter = 1
            while ServiceCategory.objects.filter(slug=self.slug).exclude(pk=self.pk).exists():
                self.slug = f"{base_slug}-{counter}"
                counter += 1
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        """URL for services in this category."""
        return reverse('venues_app:services_by_category', kwargs={'category_slug': self.slug})

    @property
    def service_count(self):
        """Count of active services in this category."""
        return self.services.filter(is_active=True).count()

    @property
    def venue_count(self):
        """Count of venues offering services in this category."""
        return self.services.filter(is_active=True).values('venue').distinct().count()


class ServiceTag(models.Model):
    """Tags for services to improve searchability and filtering."""
    
    # Tag Type Choices
    KEYWORD = 'keyword'
    TECHNIQUE = 'technique'
    BENEFIT = 'benefit'
    BODY_PART = 'body_part'
    DURATION = 'duration'
    INTENSITY = 'intensity'
    
    TAG_TYPE_CHOICES = [
        (KEYWORD, _('Keyword')),
        (TECHNIQUE, _('Technique')),
        (BENEFIT, _('Benefit')),
        (BODY_PART, _('Body Part')),
        (DURATION, _('Duration')),
        (INTENSITY, _('Intensity')),
    ]
    
    name = models.CharField(
        _('tag name'),
        max_length=50,
        unique=True,
        help_text=_('Tag name (e.g., relaxing, deep-tissue, pain-relief)')
    )
    slug = models.SlugField(
        _('slug'),
        max_length=60,
        unique=True,
        blank=True,
        null=True,
        help_text=_('URL-friendly slug (auto-generated)')
    )
    tag_type = models.CharField(
        _('tag type'),
        max_length=20,
        choices=TAG_TYPE_CHOICES,
        default=KEYWORD,
        help_text=_('Type of tag for better organization')
    )
    description = models.TextField(
        _('description'),
        blank=True,
        max_length=200,
        help_text=_('Optional description of what this tag represents')
    )
    is_active = models.BooleanField(
        _('active status'),
        default=True,
        help_text=_('Whether this tag is available for use')
    )
    usage_count = models.PositiveIntegerField(
        _('usage count'),
        default=0,
        help_text=_('Number of times this tag has been used (auto-updated)')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Service Tag')
        verbose_name_plural = _('Service Tags')
        ordering = ['tag_type', 'name']
        indexes = [
            models.Index(fields=['is_active']),
            models.Index(fields=['tag_type']),
            models.Index(fields=['usage_count']),
        ]

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        """Automatically generate slug on every save."""
        if not self.slug:
            self.slug = slugify(self.name)
        
        # Ensure slug uniqueness
        if self.slug:
            base_slug = self.slug
            counter = 1
            while ServiceTag.objects.filter(slug=self.slug).exclude(pk=self.pk).exists():
                self.slug = f"{base_slug}-{counter}"
                counter += 1
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        """URL for services with this tag."""
        return reverse('venues_app:services_by_tag', kwargs={'tag_slug': self.slug})

    def update_usage_count(self):
        """Update the usage count based on current service associations."""
        self.usage_count = self.services.filter(is_active=True).count()
        self.save(update_fields=['usage_count'])


# --- Service Operating Hours Model ---
class ServiceOperatingHours(models.Model):
    """Custom operating hours for specific services (different from venue hours)."""
    service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE,
        related_name='service_hours',
        verbose_name=_('service'),
        help_text=_('Service these hours apply to')
    )

    DAY_CHOICES = [
        (0, _('Monday')),
        (1, _('Tuesday')),
        (2, _('Wednesday')),
        (3, _('Thursday')),
        (4, _('Friday')),
        (5, _('Saturday')),
        (6, _('Sunday')),
    ]

    day = models.PositiveSmallIntegerField(
        _('day of week'),
        choices=DAY_CHOICES,
        help_text=_('Day of the week (0=Monday, 6=Sunday)')
    )
    opening = models.TimeField(
        _('opening time'),
        null=True,
        blank=True,
        help_text=_('Service available from this time')
    )
    closing = models.TimeField(
        _('closing time'),
        null=True,
        blank=True,
        help_text=_('Service available until this time')
    )
    is_closed = models.BooleanField(
        _('closed'),
        default=False,
        help_text=_('Whether the service is unavailable on this day')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Service Operating Hours')
        verbose_name_plural = _('Service Operating Hours')
        unique_together = ['service', 'day']
        ordering = ['day']

    def __str__(self):
        day_name = dict(self.DAY_CHOICES)[self.day]
        if self.is_closed:
            return f"{self.service.service_title} - {day_name}: Closed"
        return f"{self.service.service_title} - {day_name}: {self.opening}-{self.closing}"

    def clean(self):
        """Validate time fields."""
        super().clean()
        if not self.is_closed:
            if not self.opening or not self.closing:
                raise ValidationError(_('Opening and closing times are required when not closed'))
            if self.opening >= self.closing:
                raise ValidationError(_('Closing time must be after opening time'))


# --- OperatingHours Model ---
class OperatingHours(models.Model):
    """Structured business hours for venues."""
    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='operating_hours_set',
        verbose_name=_('venue'),
        help_text=_('Venue these hours belong to')
    )

    DAY_CHOICES = [
        (0, _('Monday')),
        (1, _('Tuesday')),
        (2, _('Wednesday')),
        (3, _('Thursday')),
        (4, _('Friday')),
        (5, _('Saturday')),
        (6, _('Sunday')),
    ]

    day = models.PositiveSmallIntegerField(
        _('day of week'),
        choices=DAY_CHOICES,
        help_text=_('Day of the week (0=Monday, 6=Sunday)')
    )
    opening = models.TimeField(
        _('opening time'),
        null=True,
        blank=True,
        help_text=_('Opening time for this day')
    )
    closing = models.TimeField(
        _('closing time'),
        null=True,
        blank=True,
        help_text=_('Closing time for this day')
    )
    is_closed = models.BooleanField(
        _('closed'),
        default=False,
        help_text=_('Whether the venue is closed on this day')
    )
    
    # New fields for enhanced functionality
    is_24_hours = models.BooleanField(
        _('24 hours'),
        default=False,
        help_text=_('Whether the venue is open 24 hours on this day')
    )
    is_overnight = models.BooleanField(
        _('overnight hours'),
        default=False,
        help_text=_('Whether closing time is next day (e.g., open until 2 AM)')
    )
    
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Operating Hours')
        verbose_name_plural = _('Operating Hours')
        unique_together = ['venue', 'day']
        ordering = ['day']

    def __str__(self):
        day_name = dict(self.DAY_CHOICES)[self.day]
        if self.is_closed:
            return f"{self.venue.venue_name} - {day_name}: Closed"
        if self.is_24_hours:
            return f"{self.venue.venue_name} - {day_name}: 24 Hours"
        if self.is_overnight:
            return f"{self.venue.venue_name} - {day_name}: {self.opening}-{self.closing} (next day)"
        return f"{self.venue.venue_name} - {day_name}: {self.opening}-{self.closing}"

    def clean(self):
        """Validate opening/closing times with enhanced overnight support."""
        super().clean()
        
        if self.is_closed:
            # If closed, clear other fields
            self.opening = None
            self.closing = None
            self.is_24_hours = False
            self.is_overnight = False
            return
            
        if self.is_24_hours:
            # If 24 hours, clear time fields
            self.opening = None
            self.closing = None
            self.is_overnight = False
            return
            
        # Regular hours validation
        if not self.opening or not self.closing:
            raise ValidationError(_('Opening and closing times are required when not closed or 24 hours'))
            
        # For overnight hours, closing can be before opening (e.g., 10 PM to 2 AM)
        if not self.is_overnight and self.opening >= self.closing:
            raise ValidationError(_('Opening time must be before closing time (check "overnight hours" if you close after midnight)'))


class HolidaySchedule(models.Model):
    """Special holiday hours for venues."""
    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='holiday_schedules',
        verbose_name=_('venue')
    )
    
    name = models.CharField(
        _('holiday name'),
        max_length=100,
        help_text=_('Name of the holiday or special event')
    )
    date = models.DateField(
        _('date'),
        help_text=_('Date of the special schedule')
    )
    
    # Same time fields as regular operating hours
    opening = models.TimeField(
        _('opening time'),
        null=True,
        blank=True,
        help_text=_('Opening time for this special day')
    )
    closing = models.TimeField(
        _('closing time'),
        null=True,
        blank=True,
        help_text=_('Closing time for this special day')
    )
    is_closed = models.BooleanField(
        _('closed'),
        default=False,
        help_text=_('Whether the venue is closed on this special day')
    )
    is_24_hours = models.BooleanField(
        _('24 hours'),
        default=False,
        help_text=_('Whether the venue is open 24 hours on this special day')
    )
    is_overnight = models.BooleanField(
        _('overnight hours'),
        default=False,
        help_text=_('Whether closing time is next day')
    )
    
    notes = models.TextField(
        _('notes'),
        blank=True,
        help_text=_('Additional notes about this special schedule')
    )
    
    is_active = models.BooleanField(
        _('active'),
        default=True,
        help_text=_('Whether this special schedule is active')
    )
    
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Holiday Schedule')
        verbose_name_plural = _('Holiday Schedules')
        unique_together = ['venue', 'date']
        ordering = ['date']

    def __str__(self):
        if self.is_closed:
            return f"{self.venue.venue_name} - {self.name} ({self.date}): Closed"
        if self.is_24_hours:
            return f"{self.venue.venue_name} - {self.name} ({self.date}): 24 Hours"
        return f"{self.venue.venue_name} - {self.name} ({self.date}): {self.opening}-{self.closing}"

    def clean(self):
        """Validate holiday schedule times."""
        super().clean()
        
        if self.is_closed:
            self.opening = None
            self.closing = None
            self.is_24_hours = False
            self.is_overnight = False
            return
            
        if self.is_24_hours:
            self.opening = None
            self.closing = None
            self.is_overnight = False
            return
            
        if not self.opening or not self.closing:
            raise ValidationError(_('Opening and closing times are required when not closed or 24 hours'))
            
        if not self.is_overnight and self.opening >= self.closing:
            raise ValidationError(_('Opening time must be before closing time (check "overnight hours" if you close after midnight)'))


# --- VenueAmenity Model ---
class VenueAmenity(models.Model):
    """Amenities and features available at venues."""

    # Predefined amenity choices
    WIFI = 'wifi'
    PARKING = 'parking'
    WHEELCHAIR_ACCESSIBLE = 'wheelchair_accessible'
    AIR_CONDITIONING = 'air_conditioning'
    HEATING = 'heating'
    MUSIC = 'music'
    PRIVATE_ROOMS = 'private_rooms'
    CHANGING_ROOMS = 'changing_rooms'
    SHOWER_FACILITIES = 'shower_facilities'
    REFRESHMENTS = 'refreshments'
    RETAIL_SHOP = 'retail_shop'
    ONLINE_BOOKING = 'online_booking'
    CREDIT_CARDS = 'credit_cards'
    GIFT_CERTIFICATES = 'gift_certificates'
    LOYALTY_PROGRAM = 'loyalty_program'

    AMENITY_CHOICES = [
        (WIFI, _('Wi-Fi')),
        (PARKING, _('Parking Available')),
        (WHEELCHAIR_ACCESSIBLE, _('Wheelchair Accessible')),
        (AIR_CONDITIONING, _('Air Conditioning')),
        (HEATING, _('Heating')),
        (MUSIC, _('Background Music')),
        (PRIVATE_ROOMS, _('Private Treatment Rooms')),
        (CHANGING_ROOMS, _('Changing Rooms')),
        (SHOWER_FACILITIES, _('Shower Facilities')),
        (REFRESHMENTS, _('Refreshments Available')),
        (RETAIL_SHOP, _('Retail Shop')),
        (ONLINE_BOOKING, _('Online Booking')),
        (CREDIT_CARDS, _('Credit Cards Accepted')),
        (GIFT_CERTIFICATES, _('Gift Certificates')),
        (LOYALTY_PROGRAM, _('Loyalty Program')),
    ]

    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='amenities',
        verbose_name=_('venue'),
        help_text=_('Venue this amenity belongs to')
    )
    amenity_type = models.CharField(
        _('amenity type'),
        max_length=50,
        choices=AMENITY_CHOICES,
        help_text=_('Type of amenity or feature')
    )
    custom_name = models.CharField(
        _('custom name'),
        max_length=100,
        blank=True,
        help_text=_('Custom name for the amenity (optional)')
    )
    description = models.TextField(
        _('description'),
        max_length=200,
        blank=True,
        help_text=_('Additional details about this amenity')
    )
    is_active = models.BooleanField(
        _('active'),
        default=True,
        help_text=_('Whether this amenity is currently available')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Venue Amenity')
        verbose_name_plural = _('Venue Amenities')
        unique_together = ['venue', 'amenity_type']
        ordering = ['amenity_type']

    def __str__(self):
        display_name = self.custom_name if self.custom_name else self.get_amenity_type_display()
        return f"{self.venue.venue_name} - {display_name}"

    def clean(self):
        """Validate amenity data."""
        super().clean()
        # Ensure venue doesn't have too many amenities (max 15)
        if self.venue_id:
            existing = VenueAmenity.objects.filter(venue=self.venue).exclude(pk=self.pk)
            if existing.count() >= 15:
                raise ValidationError(_('Maximum 15 amenities allowed per venue'))


# --- FlaggedVenue Model ---
class FlaggedVenue(models.Model):
    """Venues flagged by customers for admin review."""
    # Status Choices
    PENDING = 'pending'
    REVIEWED = 'reviewed'
    RESOLVED = 'resolved'
    
    STATUS_CHOICES = (
        (PENDING, _('Pending')),
        (REVIEWED, _('Reviewed')),
        (RESOLVED, _('Resolved')),
    )

    venue = models.ForeignKey(
        Venue,
        on_delete=models.CASCADE,
        related_name='flags',
        verbose_name=_('venue'),
        help_text=_('Venue that has been flagged')
    )
    flagged_by = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='flagged_venues',
        verbose_name=_('flagged by'),
        help_text=_('Customer who flagged this venue')
    )
    reason = models.TextField(
        _('reason'),
        max_length=1000,
        help_text=_('Reason for flagging this venue')
    )
    status = models.CharField(
        _('status'),
        max_length=20,
        choices=STATUS_CHOICES,
        default=PENDING,
        help_text=_('Status of the flag review')
    )
    reviewed_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_flags',
        verbose_name=_('reviewed by'),
        help_text=_('Admin who reviewed this flag')
    )
    admin_notes = models.TextField(
        _('admin notes'),
        blank=True,
        help_text=_('Admin notes about the flag review')
    )
    reviewed_at = models.DateTimeField(
        _('reviewed at'),
        null=True,
        blank=True,
        help_text=_('When the flag was reviewed')
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Flagged Venue')
        verbose_name_plural = _('Flagged Venues')
        ordering = ['-created_at']
        unique_together = ['venue', 'flagged_by']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Flag: {self.venue.venue_name} by {self.flagged_by.email}"

    def save(self, *args, **kwargs):
        """Update reviewed_at when status changes."""
        if self.status in [self.REVIEWED, self.RESOLVED] and not self.reviewed_at:
            self.reviewed_at = timezone.now()
        elif self.status == self.PENDING:
            self.reviewed_at = None
        super().save(*args, **kwargs)

    # Status Properties
    @property
    def is_pending(self):
        return self.status == self.PENDING

    @property
    def is_resolved(self):
        return self.status == self.RESOLVED

    @classmethod
    def cleanup_old_flags(cls, days=90):
        """Delete flag reports older than specified days."""
        cutoff = timezone.now() - timedelta(days=days)
        cls.objects.filter(created_at__lt=cutoff).delete()


# --- USCity Model ---
class USCity(models.Model):
    """US cities for location search and mapping with enhanced indexing and validation."""
    city = models.CharField(
        _('city'),
        max_length=100,
        help_text=_('City name'),
        db_index=True  # Add individual index for city searches
    )
    state_id = models.CharField(
        _('state abbreviation'),
        max_length=2,
        help_text=_('State abbreviation (e.g., NY, CA)'),
        db_index=True  # Add individual index for state searches
    )
    state_name = models.CharField(
        _('state name'),
        max_length=100,
        help_text=_('Full state name'),
        db_index=True  # Add individual index for state name searches
    )
    county_name = models.CharField(
        _('county name'),
        max_length=100,
        help_text=_('County name'),
        db_index=True  # Add individual index for county searches
    )
    latitude = models.DecimalField(
        _('latitude'),
        max_digits=10,
        decimal_places=7,
        help_text=_('Latitude coordinate'),
        null=True,
        blank=True
    )
    longitude = models.DecimalField(
        _('longitude'),
        max_digits=10,
        decimal_places=7,
        help_text=_('Longitude coordinate'),
        null=True,
        blank=True
    )
    zip_codes = models.TextField(
        _('ZIP codes'),
        help_text=_('Space-separated list of ZIP codes'),
        blank=True
    )
    city_id = models.CharField(
        _('city ID'),
        max_length=20,
        unique=True,
        help_text=_('Unique identifier for the city')
    )
    
    # Add fields for better search optimization
    search_vector = models.TextField(
        _('search vector'),
        help_text=_('Precomputed search text for faster queries'),
        blank=True,
        db_index=True
    )
    
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('US City')
        verbose_name_plural = _('US Cities')
        ordering = ['state_name', 'city']
        indexes = [
            # Composite indexes for common query patterns
            models.Index(fields=['state_name', 'county_name'], name='uscity_state_county_idx'),
            models.Index(fields=['state_name', 'city'], name='uscity_state_city_idx'),
            models.Index(fields=['state_name', 'county_name', 'city'], name='uscity_full_location_idx'),
            models.Index(fields=['state_id', 'county_name'], name='uscity_state_abbrev_county_idx'),
            models.Index(fields=['state_id', 'city'], name='uscity_state_abbrev_city_idx'),
            # Text search indexes
            models.Index(fields=['city', 'state_id'], name='uscity_city_state_search_idx'),
            models.Index(fields=['county_name', 'state_id'], name='uscity_county_state_search_idx'),
            # Coordinate indexes for location-based queries
            models.Index(fields=['latitude', 'longitude'], name='uscity_coordinates_idx'),
        ]
        # Add database constraints
        constraints = [
            models.UniqueConstraint(
                fields=['city', 'county_name', 'state_name'],
                name='unique_city_county_state'
            )
        ]

    def __str__(self):
        return f"{self.city}, {self.state_id}"

    def get_full_location(self):
        return f"{self.city}, {self.county_name}, {self.state_name}"
    
    def save(self, *args, **kwargs):
        """Enhanced save method with search vector optimization."""
        # Update search vector for faster text searches
        self.search_vector = f"{self.city} {self.state_name} {self.state_id} {self.county_name}".lower()
        super().save(*args, **kwargs)
    
    @classmethod
    def search_locations(cls, query, limit=10):
        """
        Enhanced location search with better performance.
        
        Args:
            query: Search query string
            limit: Maximum number of results
            
        Returns:
            QuerySet of USCity objects matching the query
        """
        if not query or len(query) < 2:
            return cls.objects.none()
        
        query = query.strip().lower()
        
        # Use the search vector for initial filtering, then apply more specific filters
        base_query = cls.objects.filter(search_vector__icontains=query)
        
        # Apply more specific filters with proper indexing
        return base_query.filter(
            Q(city__icontains=query) |
            Q(state_name__icontains=query) |
            Q(state_id__icontains=query) |
            Q(county_name__icontains=query)
        ).distinct()[:limit]
    
    def get_nearby_cities(self, radius_miles=50):
        """
        Get nearby cities within a specified radius.
        
        Args:
            radius_miles: Search radius in miles
            
        Returns:
            QuerySet of nearby USCity objects
        """
        if not self.latitude or not self.longitude:
            return USCity.objects.none()
        
        # Simple bounding box calculation (approximate)
        # 1 degree of latitude ≈ 69 miles
        # 1 degree of longitude ≈ 69 miles * cos(latitude)
        import math
        
        lat_delta = radius_miles / 69.0
        lng_delta = radius_miles / (69.0 * math.cos(math.radians(float(self.latitude))))
        
        min_lat = float(self.latitude) - lat_delta
        max_lat = float(self.latitude) + lat_delta
        min_lng = float(self.longitude) - lng_delta
        max_lng = float(self.longitude) + lng_delta
        
        return USCity.objects.filter(
            latitude__gte=min_lat,
            latitude__lte=max_lat,
            longitude__gte=min_lng,
            longitude__lte=max_lng
        ).exclude(id=self.id)


class VenueCreationDraft(models.Model):
    """Model to store venue creation progress as draft in database"""
    service_provider = models.OneToOneField(
        ServiceProviderProfile,
        on_delete=models.CASCADE,
        related_name='venue_creation_draft',
        verbose_name=_('service provider'),
        help_text=_('Service provider creating the venue')
    )
    
    # Basic Information
    venue_name = models.CharField(
        _('venue name'),
        max_length=255,
        blank=True,
        null=True,
        help_text=_('Name of the venue/business')
    )
    short_description = models.TextField(
        _('description'),
        max_length=500,
        blank=True,
        null=True,
        help_text=_('Brief description of the venue')
    )
    
    # Location Details
    state = models.CharField(
        _('state'),
        max_length=2,
        choices=Venue.STATE_CHOICES,
        blank=True,
        null=True,
        help_text=_('State where venue is located')
    )
    county = models.CharField(
        _('county'),
        max_length=100,
        blank=True,
        null=True,
        help_text=_('County where venue is located')
    )
    city = models.CharField(
        _('city'),
        max_length=100,
        blank=True,
        null=True,
        help_text=_('City where venue is located')
    )
    street_number = models.CharField(
        _('street number'),
        max_length=20,
        blank=True,
        null=True,
        help_text=_('Street number')
    )
    street_name = models.CharField(
        _('street name'),
        max_length=255,
        blank=True,
        null=True,
        help_text=_('Street name')
    )
    
    # Contact Information
    phone = models.CharField(
        _('phone'),
        max_length=20,
        blank=True,
        null=True,
        help_text=_('Contact phone number')
    )
    email = models.EmailField(
        _('email'),
        blank=True,
        null=True,
        help_text=_('Contact email address')
    )
    website_url = models.URLField(
        _('website URL'),
        blank=True,
        null=True,
        help_text=_('Website URL')
    )
    
    # Social Media
    instagram_url = models.URLField(
        _('Instagram URL'),
        blank=True,
        null=True,
        help_text=_('Instagram profile URL')
    )
    facebook_url = models.URLField(
        _('Facebook URL'),
        blank=True,
        null=True,
        help_text=_('Facebook page URL')
    )
    twitter_url = models.URLField(
        _('Twitter URL'),
        blank=True,
        null=True,
        help_text=_('Twitter profile URL')
    )
    linkedin_url = models.URLField(
        _('LinkedIn URL'),
        blank=True,
        null=True,
        help_text=_('LinkedIn profile URL')
    )
    
    # Categories - store as JSON for simplicity
    categories_data = models.JSONField(
        _('categories data'),
        default=list,
        blank=True,
        help_text=_('Selected category IDs stored as JSON')
    )

    # Operating Hours - store as JSON
    operating_hours_data = models.JSONField(
        _('operating hours data'),
        default=dict,
        blank=True,
        help_text=_('Operating hours for each day stored as JSON')
    )

    # Amenities - store as JSON
    amenities_data = models.JSONField(
        _('amenities data'),
        default=list,
        blank=True,
        help_text=_('Selected amenity types stored as JSON')
    )

    # Services - store as JSON with pricing and discounts
    services_data = models.JSONField(
        _('services data'),
        default=list,
        blank=True,
        help_text=_('Services with pricing and discount information stored as JSON')
    )

    # Team Members - store as JSON
    team_members_data = models.JSONField(
        _('team members data'),
        default=list,
        blank=True,
        help_text=_('Team member information stored as JSON')
    )

    # FAQs - store as JSON
    faqs_data = models.JSONField(
        _('FAQs data'),
        default=list,
        blank=True,
        help_text=_('Frequently asked questions stored as JSON')
    )

    # Images - store as JSON with order and metadata
    images_data = models.JSONField(
        _('images data'),
        default=list,
        blank=True,
        help_text=_('Image information and ordering stored as JSON')
    )

    # Policies and Additional Information
    cancellation_policy = models.TextField(
        _('cancellation policy'),
        max_length=1000,
        blank=True,
        null=True,
        help_text=_('Venue cancellation policy')
    )
    booking_policy = models.TextField(
        _('booking policy'),
        max_length=1000,
        blank=True,
        null=True,
        help_text=_('Venue booking policy')
    )
    special_instructions = models.TextField(
        _('special instructions'),
        max_length=500,
        blank=True,
        null=True,
        help_text=_('Special instructions for customers')
    )

    # Additional Location Details
    zip_code = models.CharField(
        _('zip code'),
        max_length=10,
        blank=True,
        null=True,
        help_text=_('Postal/ZIP code')
    )
    latitude = models.DecimalField(
        _('latitude'),
        max_digits=10,
        decimal_places=8,
        blank=True,
        null=True,
        help_text=_('Latitude coordinate')
    )
    longitude = models.DecimalField(
        _('longitude'),
        max_digits=11,
        decimal_places=8,
        blank=True,
        null=True,
        help_text=_('Longitude coordinate')
    )

    # Wizard Progress
    current_step = models.CharField(
        _('current step'),
        max_length=20,
        default='basic',
        help_text=_('Current wizard step')
    )
    completed_steps = models.JSONField(
        _('completed steps'),
        default=list,
        blank=True,
        help_text=_('List of completed wizard steps')
    )
    
    # Timestamps
    created_at = models.DateTimeField(
        _('created at'),
        auto_now_add=True,
        help_text=_('When the draft was first created')
    )
    updated_at = models.DateTimeField(
        _('updated at'),
        auto_now=True,
        help_text=_('When the draft was last updated')
    )
    
    class Meta:
        verbose_name = _('venue creation draft')
        verbose_name_plural = _('venue creation drafts')
        db_table = 'venues_venue_creation_draft'
        
    def __str__(self):
        return f"Draft for {self.service_provider.business_name} - {self.venue_name or 'Unnamed'}"
    
    def get_progress_percentage(self):
        """Calculate completion percentage based on completed steps"""
        # Updated for 5-step wizard
        total_steps = 5  # basic, location, services, gallery, details
        completed_count = len(self.completed_steps)
        return int((completed_count / total_steps) * 100) if total_steps > 0 else 0
    
    def to_dict(self):
        """Convert draft to dictionary for form initialization"""
        return {
            # Basic Information
            'venue_name': self.venue_name or '',
            'short_description': self.short_description or '',

            # Location Details
            'state': self.state or '',
            'county': self.county or '',
            'city': self.city or '',
            'street_number': self.street_number or '',
            'street_name': self.street_name or '',
            'zip_code': self.zip_code or '',
            'latitude': self.latitude,
            'longitude': self.longitude,

            # Contact Information
            'phone': self.phone or '',
            'email': self.email or '',
            'website_url': self.website_url or '',
            'instagram_url': self.instagram_url or '',
            'facebook_url': self.facebook_url or '',
            'twitter_url': self.twitter_url or '',
            'linkedin_url': self.linkedin_url or '',

            # Categories and Complex Data - return IDs for form initialization
            'categories': self.categories_data or [],
            'operating_hours': self.operating_hours_data,
            'amenities': self.amenities_data,
            'services': self.services_data,
            'team_members': self.team_members_data,
            'faqs': self.faqs_data,
            'images': self.images_data,

            # Policies
            'cancellation_policy': self.cancellation_policy or '',
            'booking_policy': self.booking_policy or '',
            'special_instructions': self.special_instructions or '',

            # Progress Tracking
            'current_step': self.current_step,
            'completed_steps': self.completed_steps,
        }

    def update_from_form_data(self, form_data, step):
        """Update draft with form data from a specific step"""
        # Update basic information
        if 'venue_name' in form_data:
            self.venue_name = form_data['venue_name']
        if 'short_description' in form_data:
            self.short_description = form_data['short_description']

        # Update location information
        if 'state' in form_data:
            self.state = form_data['state']
        if 'county' in form_data:
            self.county = form_data['county']
        if 'city' in form_data:
            self.city = form_data['city']
        if 'street_number' in form_data:
            self.street_number = form_data['street_number']
        if 'street_name' in form_data:
            self.street_name = form_data['street_name']
        if 'zip_code' in form_data:
            self.zip_code = form_data['zip_code']
        if 'latitude' in form_data:
            self.latitude = form_data['latitude']
        if 'longitude' in form_data:
            self.longitude = form_data['longitude']

        # Update contact information
        if 'phone' in form_data:
            self.phone = form_data['phone']
        if 'email' in form_data:
            self.email = form_data['email']
        if 'website_url' in form_data:
            self.website_url = form_data['website_url']

        # Update social media
        if 'instagram_url' in form_data:
            self.instagram_url = form_data['instagram_url']
        if 'facebook_url' in form_data:
            self.facebook_url = form_data['facebook_url']
        if 'twitter_url' in form_data:
            self.twitter_url = form_data['twitter_url']
        if 'linkedin_url' in form_data:
            self.linkedin_url = form_data['linkedin_url']

        # Update categories
        if 'categories' in form_data:
            if hasattr(form_data['categories'], '__iter__') and not isinstance(form_data['categories'], str):
                # Handle QuerySet or list of category objects/IDs
                category_ids = []
                for cat in form_data['categories']:
                    if hasattr(cat, 'id'):
                        category_ids.append(cat.id)
                    else:
                        category_ids.append(int(cat))
                self.categories_data = category_ids
            else:
                self.categories_data = form_data['categories']

        # Update operating hours
        if 'operating_hours' in form_data:
            self.operating_hours_data = form_data['operating_hours']

        # Update amenities
        if 'amenities' in form_data:
            self.amenities_data = form_data['amenities']

        # Update services
        if 'services' in form_data:
            self.services_data = form_data['services']

        # Update team members
        if 'team_members' in form_data:
            self.team_members_data = form_data['team_members']

        # Update FAQs
        if 'faqs' in form_data:
            self.faqs_data = form_data['faqs']

        # Update images
        if 'images' in form_data:
            self.images_data = form_data['images']

        # Update policies
        if 'cancellation_policy' in form_data:
            self.cancellation_policy = form_data['cancellation_policy']
        if 'booking_policy' in form_data:
            self.booking_policy = form_data['booking_policy']
        if 'special_instructions' in form_data:
            self.special_instructions = form_data['special_instructions']
        
        # Update progress tracking
        self.current_step = step
        if step not in self.completed_steps:
            self.completed_steps.append(step)

        self.save()

    def get_step_completion_status(self):
        """Get completion status for all steps"""
        step_order = ['basic', 'location', 'services', 'gallery', 'details']
        return {step: self.is_step_completed(step) for step in step_order}

    def is_step_completed(self, step):
        """Check if a step is completed"""
        return step in self.completed_steps

    def mark_step_completed(self, step):
        """Mark a step as completed"""
        if step not in self.completed_steps:
            self.completed_steps.append(step)
            self.save(update_fields=['completed_steps'])

    def get_next_step(self):
        """Get the next incomplete step"""
        step_order = ['basic', 'location', 'services', 'gallery', 'details']
        for step_key in step_order:
            if not self.is_step_completed(step_key):
                return step_key
        return None

    def validate_step_data(self, step):
        """Validate data for a specific step"""
        validation_errors = []

        if step == 'basic':
            if not self.venue_name:
                validation_errors.append('Venue name is required')
            if not self.short_description:
                validation_errors.append('Description is required')
            if not self.categories_data:
                validation_errors.append('At least one category is required')

        elif step == 'location':
            if not self.state:
                validation_errors.append('State is required')
            if not self.city:
                validation_errors.append('City is required')
            if not self.street_name:
                validation_errors.append('Street name is required')

        elif step == 'location':
            if not self.state:
                validation_errors.append('State is required')
            if not self.city:
                validation_errors.append('City is required')
            if not self.street_name:
                validation_errors.append('Street name is required')
            if not self.phone:
                validation_errors.append('Phone number is required')
            if not self.email:
                validation_errors.append('Email is required')

        elif step == 'services':
            if not self.services_data:
                validation_errors.append('At least one service is required')

        elif step == 'gallery':
            # Images are optional
            pass

        elif step == 'details':
            # Final validation - check all required data
            for prev_step in ['basic', 'location', 'services']:
                validation_errors.extend(self.validate_step_data(prev_step))

        return validation_errors